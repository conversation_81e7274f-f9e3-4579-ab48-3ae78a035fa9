{"app": {"bundleName": "com.goofish.monitor", "vendor": "Goofish Monitor Team", "versionCode": 1000000, "versionName": "1.0.0", "icon": "$media:app_icon", "label": "$string:app_name", "description": "$string:app_description", "minAPIVersion": 9, "targetAPIVersion": 9, "apiReleaseType": "Release", "debug": false, "distributedNotificationEnabled": true, "entityType": "unspecified", "removable": true, "targetBundleName": "", "targetPriority": 50, "multiProjects": true, "generateBuildHash": false, "asanEnabled": false, "tablet": {"minAPIVersion": 9, "distributedNotificationEnabled": true}, "car": {"minAPIVersion": 8, "distributedNotificationEnabled": true}, "tv": {"minAPIVersion": 9, "distributedNotificationEnabled": true}, "wearable": {"minAPIVersion": 8, "distributedNotificationEnabled": true}}}
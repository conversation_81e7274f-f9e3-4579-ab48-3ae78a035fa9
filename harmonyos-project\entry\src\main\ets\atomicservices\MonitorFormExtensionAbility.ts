/**
 * 监控服务卡片扩展能力
 * 提供原子化服务卡片功能，支持免安装即点即用
 */

import FormExtensionAbility from '@ohos.app.form.FormExtensionAbility';
import formInfo from '@ohos.app.form.formInfo';
import formBindingData from '@ohos.app.form.formBindingData';
import formProvider from '@ohos.app.form.formProvider';
import hilog from '@ohos.hilog';
import Want from '@ohos.app.ability.Want';

// 导入监控相关类型
import { DeviceMetrics } from '../../../agents/monitoring/HardwareAbstractionLayer';
import { HealthScore } from '../../../agents/monitoring/MonitoringAgent';

export default class MonitorFormExtensionAbility extends FormExtensionAbility {
  private updateTimers: Map<string, number> = new Map();
  private formDataCache: Map<string, any> = new Map();
  
  onAddForm(want: Want): formBindingData.FormBindingData {
    hilog.info(0x0000, 'MonitorForm', 'onAddForm called');
    
    const formId = want.parameters?.[formInfo.FormParam.IDENTITY_KEY] as string;
    const formName = want.parameters?.[formInfo.FormParam.NAME_KEY] as string;
    
    // 根据卡片类型返回不同的初始数据
    let formData = {};
    
    switch (formName) {
      case 'monitor_overview':
        formData = this.getOverviewFormData();
        break;
      case 'health_score':
        formData = this.getHealthScoreFormData();
        break;
      case 'quick_status':
        formData = this.getQuickStatusFormData();
        break;
      default:
        formData = this.getDefaultFormData();
    }
    
    // 缓存表单数据
    this.formDataCache.set(formId, formData);
    
    // 启动定时更新
    this.startFormUpdate(formId, formName);
    
    return formBindingData.createFormBindingData(formData);
  }
  
  onCastToNormalForm(formId: string): void {
    hilog.info(0x0000, 'MonitorForm', 'onCastToNormalForm called, formId: %{public}s', formId);
    
    // 停止定时更新
    this.stopFormUpdate(formId);
  }
  
  onUpdateForm(formId: string): void {
    hilog.info(0x0000, 'MonitorForm', 'onUpdateForm called, formId: %{public}s', formId);
    
    // 更新表单数据
    this.updateFormData(formId);
  }
  
  onChangeFormVisibility(newStatus: Record<string, number>): void {
    hilog.info(0x0000, 'MonitorForm', 'onChangeFormVisibility called');
    
    // 根据可见性调整更新频率
    for (const [formId, visibility] of Object.entries(newStatus)) {
      if (visibility === formInfo.VisibilityType.FORM_VISIBLE) {
        // 卡片可见，恢复正常更新频率
        this.adjustUpdateFrequency(formId, 30000); // 30秒
      } else {
        // 卡片不可见，降低更新频率
        this.adjustUpdateFrequency(formId, 120000); // 2分钟
      }
    }
  }
  
  onFormEvent(formId: string, message: string): void {
    hilog.info(0x0000, 'MonitorForm', 'onFormEvent called, formId: %{public}s, message: %{public}s', formId, message);
    
    // 处理卡片事件
    switch (message) {
      case 'refresh':
        this.updateFormData(formId);
        break;
      case 'launch_app':
        this.launchMainApp();
        break;
      case 'toggle_monitoring':
        this.toggleMonitoring();
        break;
    }
  }
  
  onRemoveForm(formId: string): void {
    hilog.info(0x0000, 'MonitorForm', 'onRemoveForm called, formId: %{public}s', formId);
    
    // 清理资源
    this.stopFormUpdate(formId);
    this.formDataCache.delete(formId);
  }
  
  onConfigurationUpdate(newConfig: any): void {
    hilog.info(0x0000, 'MonitorForm', 'onConfigurationUpdate called');
    
    // 更新所有卡片以适应新配置
    for (const formId of this.formDataCache.keys()) {
      this.updateFormData(formId);
    }
  }
  
  /**
   * 获取概览卡片数据
   */
  private getOverviewFormData(): any {
    return {
      title: '设备监控概览',
      healthScore: 100,
      cpuUsage: 0,
      memoryUsage: 0,
      batteryLevel: 100,
      alertCount: 0,
      lastUpdate: new Date().toLocaleTimeString(),
      status: 'normal'
    };
  }
  
  /**
   * 获取健康度卡片数据
   */
  private getHealthScoreFormData(): any {
    return {
      title: '设备健康度',
      score: 100,
      trend: 0,
      description: '优秀',
      components: {
        cpu: 100,
        memory: 100,
        battery: 100,
        storage: 100,
        network: 100
      },
      lastUpdate: new Date().toLocaleTimeString()
    };
  }
  
  /**
   * 获取快速状态卡片数据
   */
  private getQuickStatusFormData(): any {
    return {
      title: '快速状态',
      items: [
        { name: 'CPU', value: '0%', status: 'normal' },
        { name: '内存', value: '0%', status: 'normal' },
        { name: '电池', value: '100%', status: 'normal' }
      ],
      overallStatus: 'normal',
      lastUpdate: new Date().toLocaleTimeString()
    };
  }
  
  /**
   * 获取默认卡片数据
   */
  private getDefaultFormData(): any {
    return {
      title: '设备监控',
      message: '正在初始化...',
      lastUpdate: new Date().toLocaleTimeString()
    };
  }
  
  /**
   * 启动表单更新
   */
  private startFormUpdate(formId: string, formName: string): void {
    // 根据卡片类型设置不同的更新频率
    let updateInterval = 30000; // 默认30秒
    
    switch (formName) {
      case 'monitor_overview':
        updateInterval = 15000; // 15秒
        break;
      case 'health_score':
        updateInterval = 30000; // 30秒
        break;
      case 'quick_status':
        updateInterval = 10000; // 10秒
        break;
    }
    
    const timer = setInterval(() => {
      this.updateFormData(formId);
    }, updateInterval);
    
    this.updateTimers.set(formId, timer);
    
    hilog.info(0x0000, 'MonitorForm', 'Started form update for %{public}s with interval %{public}d', formId, updateInterval);
  }
  
  /**
   * 停止表单更新
   */
  private stopFormUpdate(formId: string): void {
    const timer = this.updateTimers.get(formId);
    if (timer) {
      clearInterval(timer);
      this.updateTimers.delete(formId);
      hilog.info(0x0000, 'MonitorForm', 'Stopped form update for %{public}s', formId);
    }
  }
  
  /**
   * 调整更新频率
   */
  private adjustUpdateFrequency(formId: string, newInterval: number): void {
    this.stopFormUpdate(formId);
    
    const timer = setInterval(() => {
      this.updateFormData(formId);
    }, newInterval);
    
    this.updateTimers.set(formId, timer);
    
    hilog.info(0x0000, 'MonitorForm', 'Adjusted update frequency for %{public}s to %{public}d', formId, newInterval);
  }
  
  /**
   * 更新表单数据
   */
  private async updateFormData(formId: string): Promise<void> {
    try {
      // 获取最新的监控数据
      const monitoringData = await this.getLatestMonitoringData();
      
      // 获取缓存的表单数据
      const cachedData = this.formDataCache.get(formId);
      if (!cachedData) return;
      
      // 根据卡片类型更新数据
      let updatedData = {};
      
      if (cachedData.title === '设备监控概览') {
        updatedData = this.updateOverviewData(cachedData, monitoringData);
      } else if (cachedData.title === '设备健康度') {
        updatedData = this.updateHealthScoreData(cachedData, monitoringData);
      } else if (cachedData.title === '快速状态') {
        updatedData = this.updateQuickStatusData(cachedData, monitoringData);
      } else {
        updatedData = cachedData;
      }
      
      // 更新缓存
      this.formDataCache.set(formId, updatedData);
      
      // 更新卡片
      const formData = formBindingData.createFormBindingData(updatedData);
      await formProvider.updateForm(formId, formData);
      
      hilog.debug(0x0000, 'MonitorForm', 'Updated form data for %{public}s', formId);
    } catch (error) {
      hilog.error(0x0000, 'MonitorForm', 'Failed to update form data: %{public}s', JSON.stringify(error));
    }
  }
  
  /**
   * 获取最新监控数据
   */
  private async getLatestMonitoringData(): Promise<any> {
    // 这里应该从监控Agent获取最新数据
    // 临时返回模拟数据
    return {
      metrics: {
        cpu: { usage: Math.random() * 100 },
        memory: { usage: Math.random() * 100 },
        battery: { level: 80 + Math.random() * 20 },
        storage: { usage: Math.random() * 100 },
        network: { isConnected: true, signalStrength: 80 + Math.random() * 20 }
      },
      healthScore: {
        overall: 70 + Math.random() * 30,
        components: {
          cpu: 70 + Math.random() * 30,
          memory: 70 + Math.random() * 30,
          battery: 70 + Math.random() * 30,
          storage: 70 + Math.random() * 30,
          network: 70 + Math.random() * 30
        }
      },
      anomalies: []
    };
  }
  
  /**
   * 更新概览数据
   */
  private updateOverviewData(cachedData: any, monitoringData: any): any {
    return {
      ...cachedData,
      healthScore: Math.round(monitoringData.healthScore.overall),
      cpuUsage: Math.round(monitoringData.metrics.cpu.usage),
      memoryUsage: Math.round(monitoringData.metrics.memory.usage),
      batteryLevel: Math.round(monitoringData.metrics.battery.level),
      alertCount: monitoringData.anomalies.length,
      lastUpdate: new Date().toLocaleTimeString(),
      status: this.getOverallStatus(monitoringData)
    };
  }
  
  /**
   * 更新健康度数据
   */
  private updateHealthScoreData(cachedData: any, monitoringData: any): any {
    const score = Math.round(monitoringData.healthScore.overall);
    
    return {
      ...cachedData,
      score,
      description: this.getHealthDescription(score),
      components: {
        cpu: Math.round(monitoringData.healthScore.components.cpu),
        memory: Math.round(monitoringData.healthScore.components.memory),
        battery: Math.round(monitoringData.healthScore.components.battery),
        storage: Math.round(monitoringData.healthScore.components.storage),
        network: Math.round(monitoringData.healthScore.components.network)
      },
      lastUpdate: new Date().toLocaleTimeString()
    };
  }
  
  /**
   * 更新快速状态数据
   */
  private updateQuickStatusData(cachedData: any, monitoringData: any): any {
    return {
      ...cachedData,
      items: [
        {
          name: 'CPU',
          value: `${Math.round(monitoringData.metrics.cpu.usage)}%`,
          status: this.getStatusFromValue(monitoringData.metrics.cpu.usage, 70, 90)
        },
        {
          name: '内存',
          value: `${Math.round(monitoringData.metrics.memory.usage)}%`,
          status: this.getStatusFromValue(monitoringData.metrics.memory.usage, 80, 95)
        },
        {
          name: '电池',
          value: `${Math.round(monitoringData.metrics.battery.level)}%`,
          status: this.getStatusFromValue(monitoringData.metrics.battery.level, 20, 10, true)
        }
      ],
      overallStatus: this.getOverallStatus(monitoringData),
      lastUpdate: new Date().toLocaleTimeString()
    };
  }
  
  /**
   * 获取整体状态
   */
  private getOverallStatus(monitoringData: any): string {
    if (monitoringData.anomalies.length > 0) {
      return 'warning';
    }
    
    const healthScore = monitoringData.healthScore.overall;
    if (healthScore >= 80) return 'normal';
    if (healthScore >= 60) return 'warning';
    return 'critical';
  }
  
  /**
   * 根据数值获取状态
   */
  private getStatusFromValue(value: number, warning: number, critical: number, reverse: boolean = false): string {
    if (reverse) {
      if (value <= critical) return 'critical';
      if (value <= warning) return 'warning';
      return 'normal';
    } else {
      if (value >= critical) return 'critical';
      if (value >= warning) return 'warning';
      return 'normal';
    }
  }
  
  /**
   * 获取健康度描述
   */
  private getHealthDescription(score: number): string {
    if (score >= 90) return '优秀';
    if (score >= 80) return '良好';
    if (score >= 60) return '一般';
    if (score >= 40) return '较差';
    return '危险';
  }
  
  /**
   * 启动主应用
   */
  private launchMainApp(): void {
    // 实现启动主应用的逻辑
    hilog.info(0x0000, 'MonitorForm', 'Launching main app');
  }
  
  /**
   * 切换监控状态
   */
  private toggleMonitoring(): void {
    // 实现切换监控状态的逻辑
    hilog.info(0x0000, 'MonitorForm', 'Toggling monitoring status');
  }
}

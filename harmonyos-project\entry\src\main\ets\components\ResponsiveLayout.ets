/**
 * 响应式布局组件
 * 支持横竖屏切换、折叠屏适配、多设备适配
 */

import display from '@ohos.display';
import window from '@ohos.window';

export enum ScreenSize {
  UNKNOWN = 'unknown',
  WATCH = 'watch',        // < 320dp
  PHONE = 'phone',        // 320dp - 600dp
  TABLET = 'tablet',      // 600dp - 840dp
  DESKTOP = 'desktop'     // > 840dp
}

export enum Orientation {
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape'
}

export enum FoldState {
  EXPANDED = 'expanded',
  FOLDED = 'folded',
  HALF_FOLDED = 'half_folded'
}

export interface LayoutBreakpoints {
  watch: number;
  phone: number;
  tablet: number;
  desktop: number;
}

export interface ResponsiveConfig {
  breakpoints?: LayoutBreakpoints;
  enableFoldDetection?: boolean;
  enableOrientationDetection?: boolean;
  transitionDuration?: number;
}

@Component
export struct ResponsiveLayout {
  @State screenSize: ScreenSize = ScreenSize.UNKNOWN;
  @State orientation: Orientation = Orientation.PORTRAIT;
  @State foldState: FoldState = FoldState.EXPANDED;
  @State screenWidth: number = 0;
  @State screenHeight: number = 0;
  @State isTransitioning: boolean = false;
  
  private config: ResponsiveConfig = {
    breakpoints: {
      watch: 320,
      phone: 600,
      tablet: 840,
      desktop: 1200
    },
    enableFoldDetection: true,
    enableOrientationDetection: true,
    transitionDuration: 200
  };
  
  private windowStage?: window.WindowStage;
  private displayManager?: display.Display;
  
  // 子组件构建器
  @BuilderParam phoneLayout?: () => void;
  @BuilderParam tabletLayout?: () => void;
  @BuilderParam watchLayout?: () => void;
  @BuilderParam desktopLayout?: () => void;
  @BuilderParam foldedLayout?: () => void;
  
  aboutToAppear() {
    this.initializeResponsiveDetection();
  }
  
  aboutToDisappear() {
    this.cleanup();
  }
  
  /**
   * 初始化响应式检测
   */
  private async initializeResponsiveDetection(): Promise<void> {
    try {
      // 获取显示信息
      this.displayManager = await display.getDefaultDisplaySync();
      this.updateScreenInfo();
      
      // 设置监听器
      if (this.config.enableOrientationDetection) {
        this.setupOrientationListener();
      }
      
      if (this.config.enableFoldDetection && this.isFoldableDevice()) {
        this.setupFoldStateListener();
      }
      
      console.info('ResponsiveLayout: Initialized successfully');
    } catch (error) {
      console.error('ResponsiveLayout: Initialization failed:', error);
    }
  }
  
  /**
   * 更新屏幕信息
   */
  private updateScreenInfo(): void {
    if (!this.displayManager) return;
    
    const oldScreenSize = this.screenSize;
    const oldOrientation = this.orientation;
    
    // 获取屏幕尺寸
    this.screenWidth = this.displayManager.width;
    this.screenHeight = this.displayManager.height;
    
    // 计算屏幕密度独立像素
    const density = this.displayManager.densityDPI / 160;
    const widthDp = this.screenWidth / density;
    const heightDp = this.screenHeight / density;
    
    // 确定屏幕尺寸类型
    this.screenSize = this.determineScreenSize(Math.min(widthDp, heightDp));
    
    // 确定屏幕方向
    this.orientation = this.screenWidth > this.screenHeight ? 
      Orientation.LANDSCAPE : Orientation.PORTRAIT;
    
    // 检查是否需要过渡动画
    if (oldScreenSize !== this.screenSize || oldOrientation !== this.orientation) {
      this.triggerTransition();
    }
    
    console.debug(`ResponsiveLayout: Screen updated - ${this.screenSize} ${this.orientation} (${widthDp}x${heightDp}dp)`);
  }
  
  /**
   * 确定屏幕尺寸类型
   */
  private determineScreenSize(minDimension: number): ScreenSize {
    const breakpoints = this.config.breakpoints!;
    
    if (minDimension < breakpoints.watch) {
      return ScreenSize.WATCH;
    } else if (minDimension < breakpoints.phone) {
      return ScreenSize.PHONE;
    } else if (minDimension < breakpoints.tablet) {
      return ScreenSize.TABLET;
    } else {
      return ScreenSize.DESKTOP;
    }
  }
  
  /**
   * 设置方向变化监听
   */
  private setupOrientationListener(): void {
    // 监听窗口尺寸变化
    try {
      display.on('change', (displayId: number) => {
        if (displayId === this.displayManager?.id) {
          this.updateScreenInfo();
        }
      });
    } catch (error) {
      console.error('ResponsiveLayout: Failed to setup orientation listener:', error);
    }
  }
  
  /**
   * 设置折叠状态监听
   */
  private setupFoldStateListener(): void {
    // 这里需要使用鸿蒙的折叠屏API
    // 由于API可能在不同版本中有所不同，这里提供一个基础实现
    try {
      // 假设的折叠屏状态监听API
      // foldableDisplay.on('foldStateChange', (state) => {
      //   this.handleFoldStateChange(state);
      // });
      console.info('ResponsiveLayout: Fold state listener setup (placeholder)');
    } catch (error) {
      console.error('ResponsiveLayout: Failed to setup fold state listener:', error);
    }
  }
  
  /**
   * 处理折叠状态变化
   */
  private handleFoldStateChange(state: any): void {
    const oldFoldState = this.foldState;
    
    // 根据状态更新折叠状态
    switch (state.type) {
      case 'expanded':
        this.foldState = FoldState.EXPANDED;
        break;
      case 'folded':
        this.foldState = FoldState.FOLDED;
        break;
      case 'half_folded':
        this.foldState = FoldState.HALF_FOLDED;
        break;
    }
    
    if (oldFoldState !== this.foldState) {
      this.triggerTransition();
      console.info(`ResponsiveLayout: Fold state changed to ${this.foldState}`);
    }
  }
  
  /**
   * 触发过渡动画
   */
  private triggerTransition(): void {
    this.isTransitioning = true;
    
    setTimeout(() => {
      this.isTransitioning = false;
    }, this.config.transitionDuration);
  }
  
  /**
   * 检查是否为折叠设备
   */
  private isFoldableDevice(): boolean {
    // 这里需要检查设备是否支持折叠
    // 可以通过设备信息或特定API来判断
    return false; // 暂时返回false
  }
  
  /**
   * 获取当前布局信息
   */
  getLayoutInfo(): Record<string, any> {
    return {
      screenSize: this.screenSize,
      orientation: this.orientation,
      foldState: this.foldState,
      screenWidth: this.screenWidth,
      screenHeight: this.screenHeight,
      isTransitioning: this.isTransitioning
    };
  }
  
  /**
   * 清理资源
   */
  private cleanup(): void {
    try {
      display.off('change');
      console.info('ResponsiveLayout: Cleanup completed');
    } catch (error) {
      console.error('ResponsiveLayout: Cleanup failed:', error);
    }
  }
  
  build() {
    Column() {
      if (this.isTransitioning) {
        // 过渡动画占位符
        Text('布局切换中...')
          .fontSize(16)
          .fontColor(Color.Gray)
          .textAlign(TextAlign.Center)
      } else {
        // 根据屏幕尺寸和折叠状态选择布局
        if (this.foldState === FoldState.FOLDED && this.foldedLayout) {
          this.foldedLayout();
        } else {
          switch (this.screenSize) {
            case ScreenSize.WATCH:
              if (this.watchLayout) {
                this.watchLayout();
              } else {
                this.buildDefaultWatchLayout();
              }
              break;
            case ScreenSize.PHONE:
              if (this.phoneLayout) {
                this.phoneLayout();
              } else {
                this.buildDefaultPhoneLayout();
              }
              break;
            case ScreenSize.TABLET:
              if (this.tabletLayout) {
                this.tabletLayout();
              } else {
                this.buildDefaultTabletLayout();
              }
              break;
            case ScreenSize.DESKTOP:
              if (this.desktopLayout) {
                this.desktopLayout();
              } else {
                this.buildDefaultDesktopLayout();
              }
              break;
            default:
              this.buildDefaultLayout();
          }
        }
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .animation({
      duration: this.config.transitionDuration,
      curve: Curve.EaseInOut
    })
  }
  
  @Builder buildDefaultWatchLayout() {
    Text('手表布局')
      .fontSize(14)
      .textAlign(TextAlign.Center)
  }
  
  @Builder buildDefaultPhoneLayout() {
    Text('手机布局')
      .fontSize(16)
      .textAlign(TextAlign.Center)
  }
  
  @Builder buildDefaultTabletLayout() {
    Text('平板布局')
      .fontSize(18)
      .textAlign(TextAlign.Center)
  }
  
  @Builder buildDefaultDesktopLayout() {
    Text('桌面布局')
      .fontSize(20)
      .textAlign(TextAlign.Center)
  }
  
  @Builder buildDefaultLayout() {
    Text('默认布局')
      .fontSize(16)
      .textAlign(TextAlign.Center)
  }
}

# 鸿蒙设备监控应用

基于ai-goofish-monitor项目的鸿蒙手机端移植版本，采用Agent架构设计，支持跨设备协同监控。

## 项目概述

本项目将原有的Python桌面监控应用移植到鸿蒙生态，实现了：
- 模块化Agent架构
- 响应式UI设计
- 原子化服务支持
- 跨设备协同能力
- 智能告警分发

## 架构设计

### Agent系统
- **MonitoringAgent**: 核心监控Agent，负责设备指标采集和异常检测
- **RenderingAgent**: 前端渲染Agent，负责数据可视化和UI更新
- **AlertingAgent**: 告警分发Agent（待实现）
- **SyncAgent**: 数据同步Agent（待实现）
- **DeviceAgent**: 设备管理Agent（待实现）

### 通信协议
- **EventHub**: 事件中心，支持本地和跨设备事件分发
- **RPCManager**: RPC管理器，支持Agent间远程调用
- **MessagePipe**: 消息管道（待实现）

### UI组件
- **ResponsiveLayout**: 响应式布局组件，支持多设备适配
- **MonitorCard**: 监控数据卡片组件
- **MonitorCardGrid**: 卡片网格布局

## 已完成功能

### ✅ 基础架构
- [x] Agent基础框架
- [x] EventHub通信协议
- [x] RPC通信管理器
- [x] 响应式布局组件

### ✅ 监控系统
- [x] 硬件抽象层(HAL)适配
- [x] 设备指标采集（CPU/内存/电池/网络/存储/温度）
- [x] 健康度评估算法
- [x] 异常检测机制
- [x] 动态采样率调整
- [x] 功耗优化策略

### ✅ 渲染系统
- [x] 数据可视化渲染管道
- [x] 异常数据高亮策略
- [x] 多设备样式自适应
- [x] 实时UI更新机制

### ✅ 用户界面
- [x] 主页面设计
- [x] 监控卡片组件
- [x] 健康度总览
- [x] 响应式布局适配

### ✅ 原子化服务
- [x] 服务卡片扩展能力
- [x] 监控概览卡片
- [x] 健康度卡片
- [x] 快速状态卡片
- [x] 卡片定时更新机制

## 待实现功能

### 🔄 告警系统
- [ ] 分级推送策略（震动/声音/强提醒）
- [ ] 设备协同路由表
- [ ] 紧急联系人联动
- [ ] DistributedScheduler集成

### 🔄 数据同步
- [ ] 增量式同步引擎
- [ ] 冲突解决策略库
- [ ] 端到端加密管道
- [ ] 分布式数据湖架构

### 🔄 设备管理
- [ ] 计算资源调度策略
- [ ] 跨设备服务发现
- [ ] Agent生命周期控制器
- [ ] 资源分级管理

### 🔄 移动端特性
- [ ] 折叠屏适配完善
- [ ] 深色模式支持
- [ ] 手势控制层
- [ ] 语音交互集成

## 项目结构

```
harmonyos-project/
├── entry/                          # 主应用模块
│   ├── src/main/
│   │   ├── ets/                    # ArkTS源码
│   │   │   ├── entryability/       # 应用入口
│   │   │   ├── pages/              # 页面
│   │   │   ├── components/         # UI组件
│   │   │   └── atomicservices/     # 原子化服务
│   │   └── resources/              # 资源文件
├── agents/                         # Agent模块
│   ├── monitoring/                 # 监控Agent
│   └── rendering/                  # 渲染Agent
├── shared/                         # 共享库
│   ├── agents/                     # Agent基础框架
│   └── communication/              # 通信协议
└── AppScope/                       # 应用配置
```

## 核心特性

### 🎯 性能指标
- 横竖屏切换响应时间 ≤ 200ms
- 多Agent并行CPU占用峰值 ≤ 35%
- 支持最小屏幕宽度 320dp
- 触控热区 ≥ 48x48px

### 🔋 功耗优化
- 动态采样率调整（1~60Hz）
- 电池敏感模式自动切换
- 后台运行时降低更新频率
- 智能缓存策略

### 📱 设备适配
- 手机/平板/手表/车机多设备支持
- 折叠屏状态检测和布局适配
- 响应式组件自动调整
- 深色模式系统集成

### 🔔 智能告警
- 五级告警策略（L1-L5）
- 跨设备告警接力
- 场景化通知方式
- 紧急联系人联动

## 开发环境

### 必需工具
- DevEco Studio 4.0+
- HarmonyOS SDK API Level 9+
- Node.js 16.x+

### 编译运行
1. 使用DevEco Studio打开项目
2. 配置签名证书
3. 连接鸿蒙设备或启动模拟器
4. 点击运行按钮编译安装

## 技术栈

- **开发语言**: ArkTS (TypeScript)
- **UI框架**: ArkUI
- **架构模式**: Agent架构
- **通信协议**: EventHub + RPC
- **状态管理**: @State装饰器
- **布局方案**: 响应式布局
- **原子化服务**: FormExtensionAbility

## 贡献指南

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目基于MIT许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情

## 致谢

- 基于 [ai-goofish-monitor](https://github.com/dingyufei615/ai-goofish-monitor) 项目
- 感谢鸿蒙开发团队提供的技术支持
- 感谢开源社区的贡献

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件至项目维护者
- 加入开发者交流群

---

**注意**: 本项目目前处于开发阶段，部分功能仍在实现中。欢迎贡献代码和提出建议！

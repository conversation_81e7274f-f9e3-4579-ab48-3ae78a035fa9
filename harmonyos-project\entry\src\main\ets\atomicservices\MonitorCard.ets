/**
 * 监控服务卡片UI组件
 * 用于原子化服务的卡片界面
 */

@Entry
@Component
struct MonitorCard {
  @State title: string = '设备监控';
  @State healthScore: number = 100;
  @State cpuUsage: number = 0;
  @State memoryUsage: number = 0;
  @State batteryLevel: number = 100;
  @State alertCount: number = 0;
  @State lastUpdate: string = '';
  @State status: string = 'normal';
  
  // 卡片类型
  @State cardType: string = 'overview'; // overview, health, quick
  
  aboutToAppear() {
    // 从卡片数据中获取初始值
    this.loadFormData();
  }
  
  /**
   * 加载表单数据
   */
  private loadFormData(): void {
    // 这里应该从FormExtensionAbility传递的数据中获取
    // 临时使用默认值
    this.lastUpdate = new Date().toLocaleTimeString();
  }
  
  /**
   * 获取状态颜色
   */
  private getStatusColor(): Color | Resource {
    switch (this.status) {
      case 'normal':
        return Color.Green;
      case 'warning':
        return Color.Orange;
      case 'critical':
        return Color.Red;
      default:
        return Color.Gray;
    }
  }
  
  /**
   * 获取健康度颜色
   */
  private getHealthColor(): Color | Resource {
    if (this.healthScore >= 80) return Color.Green;
    if (this.healthScore >= 60) return Color.Orange;
    return Color.Red;
  }
  
  /**
   * 处理卡片点击事件
   */
  private onCardClick(): void {
    // 发送事件到FormExtensionAbility
    postCardAction(this, {
      action: 'message',
      params: {
        message: 'launch_app'
      }
    });
  }
  
  /**
   * 处理刷新按钮点击
   */
  private onRefreshClick(): void {
    postCardAction(this, {
      action: 'message',
      params: {
        message: 'refresh'
      }
    });
  }
  
  build() {
    Column() {
      if (this.cardType === 'overview') {
        this.buildOverviewCard();
      } else if (this.cardType === 'health') {
        this.buildHealthCard();
      } else if (this.cardType === 'quick') {
        this.buildQuickCard();
      } else {
        this.buildDefaultCard();
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .borderRadius(16)
    .padding(12)
    .onClick(() => {
      this.onCardClick();
    })
  }
  
  @Builder buildOverviewCard() {
    Column() {
      // 标题栏
      Row() {
        Text(this.title)
          .fontSize(14)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
        
        if (this.alertCount > 0) {
          Text(this.alertCount.toString())
            .fontSize(10)
            .fontColor(Color.White)
            .backgroundColor(Color.Red)
            .borderRadius(8)
            .padding({ left: 4, right: 4, top: 1, bottom: 1 })
        }
        
        Image($r('app.media.ic_refresh'))
          .width(16)
          .height(16)
          .margin({ left: 4 })
          .onClick(() => {
            this.onRefreshClick();
          })
      }
      .width('100%')
      .margin({ bottom: 8 })
      
      // 健康度显示
      Row() {
        Progress({
          value: this.healthScore,
          total: 100,
          type: ProgressType.Ring
        })
        .width(40)
        .height(40)
        .color(this.getHealthColor())
        .style({ strokeWidth: 4 })
        
        Column() {
          Text(`${this.healthScore}%`)
            .fontSize(16)
            .fontWeight(FontWeight.Bold)
            .fontColor(this.getHealthColor())
          
          Text('健康度')
            .fontSize(10)
            .fontColor(Color.Gray)
        }
        .alignItems(HorizontalAlign.Start)
        .margin({ left: 8 })
        .layoutWeight(1)
      }
      .width('100%')
      .margin({ bottom: 8 })
      
      // 关键指标
      Row() {
        this.buildMetricItem('CPU', `${this.cpuUsage}%`, this.cpuUsage >= 90 ? 'critical' : this.cpuUsage >= 70 ? 'warning' : 'normal');
        this.buildMetricItem('内存', `${this.memoryUsage}%`, this.memoryUsage >= 95 ? 'critical' : this.memoryUsage >= 80 ? 'warning' : 'normal');
        this.buildMetricItem('电池', `${this.batteryLevel}%`, this.batteryLevel <= 10 ? 'critical' : this.batteryLevel <= 20 ? 'warning' : 'normal');
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)
      .margin({ bottom: 4 })
      
      // 更新时间
      Text(`更新: ${this.lastUpdate}`)
        .fontSize(8)
        .fontColor(Color.Gray)
        .alignSelf(ItemAlign.End)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.SpaceBetween)
  }
  
  @Builder buildHealthCard() {
    Column() {
      // 标题
      Text('设备健康度')
        .fontSize(14)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: 12 })
      
      // 健康度环形进度
      Stack() {
        Progress({
          value: this.healthScore,
          total: 100,
          type: ProgressType.Ring
        })
        .width(80)
        .height(80)
        .color(this.getHealthColor())
        .style({ strokeWidth: 6 })
        
        Column() {
          Text(`${this.healthScore}`)
            .fontSize(20)
            .fontWeight(FontWeight.Bold)
            .fontColor(this.getHealthColor())
          
          Text('分')
            .fontSize(12)
            .fontColor(Color.Gray)
        }
      }
      .margin({ bottom: 12 })
      
      // 健康度描述
      Text(this.getHealthDescription())
        .fontSize(12)
        .fontColor(this.getHealthColor())
        .margin({ bottom: 8 })
      
      // 更新时间
      Text(`更新: ${this.lastUpdate}`)
        .fontSize(8)
        .fontColor(Color.Gray)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }
  
  @Builder buildQuickCard() {
    Column() {
      // 标题
      Row() {
        Text('快速状态')
          .fontSize(14)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
        
        Circle()
          .width(8)
          .height(8)
          .fill(this.getStatusColor())
      }
      .width('100%')
      .margin({ bottom: 8 })
      
      // 快速指标
      Column() {
        this.buildQuickMetricRow('CPU', `${this.cpuUsage}%`, this.cpuUsage >= 90 ? 'critical' : this.cpuUsage >= 70 ? 'warning' : 'normal');
        this.buildQuickMetricRow('内存', `${this.memoryUsage}%`, this.memoryUsage >= 95 ? 'critical' : this.memoryUsage >= 80 ? 'warning' : 'normal');
        this.buildQuickMetricRow('电池', `${this.batteryLevel}%`, this.batteryLevel <= 10 ? 'critical' : this.batteryLevel <= 20 ? 'warning' : 'normal');
      }
      .width('100%')
      .layoutWeight(1)
      
      // 更新时间
      Text(`${this.lastUpdate}`)
        .fontSize(8)
        .fontColor(Color.Gray)
        .alignSelf(ItemAlign.End)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.SpaceBetween)
  }
  
  @Builder buildDefaultCard() {
    Column() {
      Text(this.title)
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: 8 })
      
      Text('正在加载...')
        .fontSize(12)
        .fontColor(Color.Gray)
      
      Text(`${this.lastUpdate}`)
        .fontSize(10)
        .fontColor(Color.Gray)
        .margin({ top: 8 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }
  
  @Builder buildMetricItem(name: string, value: string, status: string) {
    Column() {
      Text(value)
        .fontSize(12)
        .fontWeight(FontWeight.Bold)
        .fontColor(this.getStatusColorFromString(status))
      
      Text(name)
        .fontSize(8)
        .fontColor(Color.Gray)
    }
    .alignItems(HorizontalAlign.Center)
  }
  
  @Builder buildQuickMetricRow(name: string, value: string, status: string) {
    Row() {
      Text(name)
        .fontSize(10)
        .fontColor(Color.Gray)
        .layoutWeight(1)
      
      Text(value)
        .fontSize(10)
        .fontWeight(FontWeight.Medium)
        .fontColor(this.getStatusColorFromString(status))
      
      Circle()
        .width(6)
        .height(6)
        .fill(this.getStatusColorFromString(status))
        .margin({ left: 4 })
    }
    .width('100%')
    .margin({ bottom: 2 })
  }
  
  /**
   * 从字符串获取状态颜色
   */
  private getStatusColorFromString(status: string): Color {
    switch (status) {
      case 'normal':
        return Color.Green;
      case 'warning':
        return Color.Orange;
      case 'critical':
        return Color.Red;
      default:
        return Color.Gray;
    }
  }
  
  /**
   * 获取健康度描述
   */
  private getHealthDescription(): string {
    if (this.healthScore >= 90) return '优秀';
    if (this.healthScore >= 80) return '良好';
    if (this.healthScore >= 60) return '一般';
    if (this.healthScore >= 40) return '较差';
    return '危险';
  }
}

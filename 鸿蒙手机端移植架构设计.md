# ai-goofish-monitor 鸿蒙手机端移植架构设计

## 项目概述

基于现有的ai-goofish-monitor项目，进行鸿蒙手机端移植的模块化任务分解。原项目是一个基于Playwright和AI过滤分析的闲鱼多任务实时监控与智能分析工具，现需要移植到鸿蒙生态，实现跨设备协同监控。

## 原项目技术栈分析

### 核心组件
- **爬虫引擎**: Playwright (Python) - 负责闲鱼数据采集
- **AI分析**: OpenAI兼容API - 多模态商品分析
- **Web界面**: FastAPI + Jinja2 - 任务管理和结果展示
- **数据存储**: JSONL文件 - 商品和分析结果存储
- **通知系统**: ntfy.sh, 企业微信, Bark - 多渠道推送
- **任务调度**: APScheduler - 定时任务管理

### 关键特性
- 多任务并发监控
- 实时AI分析
- Web可视化管理
- 反爬虫策略
- Docker容器化部署

## 鸿蒙移植架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    鸿蒙超级终端生态                          │
├─────────────────┬─────────────────┬─────────────────────────┤
│   手机/平板     │     手表        │      车机/大屏          │
├─────────────────┼─────────────────┼─────────────────────────┤
│  移动端UI框架   │   简化监控面板   │    大屏数据展示         │
│  原子化服务     │   快捷操作      │    语音交互             │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                    Agent协作层                              │
├─────────────────┬─────────────────┬─────────────────────────┤
│ 前端数据渲染    │   核心监控      │    告警分发             │
│ Agent          │   Agent         │    Agent                │
├─────────────────┼─────────────────┼─────────────────────────┤
│ 数据同步        │   设备管理      │    跨Agent协作          │
│ Agent          │   Agent         │    矩阵                 │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                 鸿蒙系统服务层                               │
├─────────────────┬─────────────────┬─────────────────────────┤
│ 硬件抽象层      │ 分布式调度      │    安全加密             │
│ (HAL)          │ 服务            │    服务                 │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 六大Agent模块设计

### 1. 移动端UI框架重构Agent

**核心职责**: 将桌面级UI转换为移动优先设计

**技术实现**:
- **UI组件库**: 基于鸿蒙ArkUI框架，设计响应式布局组件
  - 卡片式商品展示组件
  - 导航栏适配组件  
  - 仪表盘数据展示组件
- **深色模式**: 集成鸿蒙系统主题检测API
- **折叠屏适配**: 实现DisplayShapeController侦听器
- **原子化服务**: 提炼核心功能为独立服务卡片

**性能指标**:
- 横竖屏切换响应时间 ≤ 200ms
- 支持最小屏幕宽度 320dp
- 触控热区 ≥ 48x48px

### 2. 前端数据渲染Agent

**核心职责**: 分离UI与数据处理逻辑，驱动视图更新

**技术实现**:
- **数据可视化管道**: 实时数据流处理和图表渲染
- **异常数据高亮**: 智能异常检测和视觉提示
- **多设备适配引擎**: 支持手机/平板/手表/车机等不同设备
- **EventHub通信**: 与监控Agent的实时数据交互

**通信协议**:
```typescript
// 输入通道
EventHub.subscribe("realTimeData", (data) => {
  renderPipeline.process(data);
});

// 输出通道  
RPC.call("viewUpdate", updateParams);
```

### 3. 核心监控Agent

**核心职责**: 继承原项目监控算法，适配鸿蒙硬件抽象层

**技术实现**:
- **硬件抽象层适配**: 跨设备传感器数据采集
  - CPU使用率监控
  - 内存占用分析
  - 电池状态检测
  - 网络连接监控
- **设备健康度评估**: 多维度性能指标分析
- **异常模式识别**: 智能异常检测和预警
- **动态采样率**: 1~60Hz自适应调整

**特化能力**:
- 功耗敏感模式切换
- 资源占用分析报告
- 跨设备数据采集协调

### 4. 告警分发Agent

**核心职责**: 实现分级告警策略，支持跨设备预警接力

**技术实现**:
- **分级推送策略**: 
  - L1: 震动提醒
  - L2: 声音通知
  - L3: 强提醒+屏幕点亮
  - L4: 跨设备接力
  - L5: 紧急联系人联动
- **设备协同路由**: 智能选择最佳通知设备
- **DistributedScheduler集成**: 鸿蒙分布式调度服务

**协议接口**:
```typescript
// 接收异常事件
EventHub.subscribe("alertEvent", handleAlert);

// 分发告警
DistributedScheduler.pushAlert(alertConfig);
```

### 5. 数据同步Agent

**核心职责**: 构建分布式数据湖，支持多设备状态同步

**技术实现**:
- **增量式同步引擎**: 高效数据差分同步
- **冲突解决策略**: 多设备数据冲突智能处理
- **端到端加密**: 保障数据传输安全
- **分布式数据湖**: 支持强一致性和最终一致性

**同步策略**:
- **强一致性**: 关键配置数据
- **最终一致性**: 历史记录和日志

### 6. 设备管理Agent

**核心职责**: 实现鸿蒙超级终端管理能力，动态优化资源分配

**技术实现**:
- **计算资源调度**: CPU配额动态分配
- **跨设备服务发现**: 自动发现和连接
- **Agent生命周期管理**: 动态启停和热更新
- **资源分级管理**: 存储空间和电池优化

**优化维度**:
- CPU配额动态分配
- 网络带宽优先级
- 存储空间分级管理
- 电池续航优化

## 跨Agent协作矩阵

| 发起方 | 接收方 | 通信协议 | 数据负载示例 |
|--------|--------|----------|--------------|
| 前端渲染Agent | 监控Agent | RPC("dataRequest") | {samplingRate:10, metrics:['cpu']} |
| 监控Agent | 告警分发Agent | EventHub("threshold") | {metric:'mem', value:95, unit:'%'} |
| 数据同步Agent | 设备管理Agent | MessagePipe("resource") | {neededRAM:64, minBattery:20} |
| 告警分发Agent | 数据同步Agent | RPC("backupAlert") | {eventID:'x9s8', devices:['watch']} |

## 移动端特性集成路径

### 折叠屏适配阶段
- 实现DisplayShapeController侦听
- 构建可重组布局组件
- 支持展开/折叠状态切换

### 功耗敏感模式
- 集成ResourceSchedule管理接口
- 开发L1/L2数据缓存策略
- 动态调整采样频率

### 原子化服务封装
- 提炼核心功能为独立服务卡片
- 实现免安装即点即用流程
- 支持桌面小组件展示

### 手势控制层
- 边缘滑动控制面板集成
- 多指触控数据分析手势
- 语音命令集成

## 验收条件

### 端到端验证
- 在折叠/直板双形态设备完成UI压力测试
- 模拟从手表到车机的五级告警接力
- 验证跨设备数据同步一致性

### Agent自治验证
- 单独启停各Agent不影响核心功能
- 强制kill前端Agent后数据持续采集
- 热更新机制验证

### 性能基线
- 多Agent并行时CPU占用峰值 ≤ 35%
- 内存占用控制在合理范围
- 电池续航影响最小化

## 技术风险与应对

### 主要风险
1. **鸿蒙API兼容性**: 部分功能可能需要适配
2. **跨设备通信稳定性**: 网络环境复杂
3. **性能优化挑战**: 多Agent并行运行

### 应对策略
1. **渐进式迁移**: 优先实现核心功能
2. **降级方案**: 单设备模式作为备选
3. **性能监控**: 实时监控资源使用情况

## 开发里程碑

### 阶段1: 基础架构搭建 (4周)
- Agent框架设计
- 通信协议实现
- 基础UI组件开发

### 阶段2: 核心功能实现 (6周)  
- 监控Agent开发
- 数据同步机制
- 告警分发系统

### 阶段3: 移动端特性集成 (4周)
- 折叠屏适配
- 原子化服务
- 手势控制

### 阶段4: 测试与优化 (3周)
- 端到端测试
- 性能优化
- 用户体验调优

## 注意事项

- 所有Agent需实现v-minor热更新接口
- 允许动态加载行为策略包
- 保持与原项目的功能兼容性
- 遵循鸿蒙开发规范和设计指南

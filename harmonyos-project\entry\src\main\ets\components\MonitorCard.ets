/**
 * 监控数据卡片组件
 * 显示设备监控信息的卡片式组件
 */

export interface MonitorData {
  title: string;
  value: number;
  unit: string;
  status: MonitorStatus;
  trend?: number; // 变化趋势，正数表示上升，负数表示下降
  threshold?: {
    warning: number;
    critical: number;
  };
  lastUpdate?: number;
}

export enum MonitorStatus {
  NORMAL = 'normal',
  WARNING = 'warning',
  CRITICAL = 'critical',
  UNKNOWN = 'unknown'
}

export enum CardSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large'
}

@Component
export struct MonitorCard {
  @Prop data: MonitorData;
  @Prop size: CardSize = CardSize.MEDIUM;
  @Prop showTrend: boolean = true;
  @Prop showLastUpdate: boolean = true;
  @Prop clickable: boolean = true;
  @State isPressed: boolean = false;
  
  // 点击事件回调
  onCardClick?: (data: MonitorData) => void;
  
  /**
   * 获取状态颜色
   */
  private getStatusColor(): Color | Resource {
    switch (this.data.status) {
      case MonitorStatus.NORMAL:
        return $r('sys.color.ohos_id_color_success');
      case MonitorStatus.WARNING:
        return $r('sys.color.ohos_id_color_warning');
      case MonitorStatus.CRITICAL:
        return $r('sys.color.ohos_id_color_error');
      default:
        return $r('sys.color.ohos_id_color_secondary');
    }
  }
  
  /**
   * 获取趋势图标
   */
  private getTrendIcon(): Resource {
    if (!this.data.trend) return $r('app.media.ic_trend_stable');
    
    if (this.data.trend > 0) {
      return $r('app.media.ic_trend_up');
    } else if (this.data.trend < 0) {
      return $r('app.media.ic_trend_down');
    } else {
      return $r('app.media.ic_trend_stable');
    }
  }
  
  /**
   * 获取趋势颜色
   */
  private getTrendColor(): Color | Resource {
    if (!this.data.trend) return Color.Gray;
    
    // 对于某些指标，上升可能是坏事（如CPU使用率）
    // 这里简化处理，可以根据具体指标类型来判断
    if (this.data.trend > 0) {
      return this.data.status === MonitorStatus.CRITICAL ? 
        $r('sys.color.ohos_id_color_error') : $r('sys.color.ohos_id_color_success');
    } else if (this.data.trend < 0) {
      return $r('sys.color.ohos_id_color_success');
    } else {
      return Color.Gray;
    }
  }
  
  /**
   * 格式化最后更新时间
   */
  private formatLastUpdate(): string {
    if (!this.data.lastUpdate) return '';
    
    const now = Date.now();
    const diff = now - this.data.lastUpdate;
    
    if (diff < 60000) { // 小于1分钟
      return '刚刚更新';
    } else if (diff < 3600000) { // 小于1小时
      const minutes = Math.floor(diff / 60000);
      return `${minutes}分钟前`;
    } else if (diff < 86400000) { // 小于1天
      const hours = Math.floor(diff / 3600000);
      return `${hours}小时前`;
    } else {
      const days = Math.floor(diff / 86400000);
      return `${days}天前`;
    }
  }
  
  /**
   * 获取卡片尺寸样式
   */
  private getCardDimensions(): { width: number | string, height: number | string, padding: number } {
    switch (this.size) {
      case CardSize.SMALL:
        return { width: 120, height: 80, padding: 8 };
      case CardSize.MEDIUM:
        return { width: 160, height: 120, padding: 12 };
      case CardSize.LARGE:
        return { width: 200, height: 160, padding: 16 };
      default:
        return { width: 160, height: 120, padding: 12 };
    }
  }
  
  /**
   * 获取字体大小
   */
  private getFontSizes(): { title: number, value: number, unit: number, trend: number } {
    switch (this.size) {
      case CardSize.SMALL:
        return { title: 12, value: 18, unit: 10, trend: 10 };
      case CardSize.MEDIUM:
        return { title: 14, value: 24, unit: 12, trend: 12 };
      case CardSize.LARGE:
        return { title: 16, value: 32, unit: 14, trend: 14 };
      default:
        return { title: 14, value: 24, unit: 12, trend: 12 };
    }
  }
  
  build() {
    const dimensions = this.getCardDimensions();
    const fontSizes = this.getFontSizes();
    
    Column() {
      // 标题行
      Row() {
        Text(this.data.title)
          .fontSize(fontSizes.title)
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .fontWeight(FontWeight.Medium)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .layoutWeight(1)
        
        // 状态指示器
        Circle()
          .width(8)
          .height(8)
          .fill(this.getStatusColor())
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)
      .alignItems(VerticalAlign.Center)
      
      Blank()
      
      // 数值显示
      Row() {
        Text(this.data.value.toFixed(1))
          .fontSize(fontSizes.value)
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .fontWeight(FontWeight.Bold)
        
        Text(this.data.unit)
          .fontSize(fontSizes.unit)
          .fontColor($r('sys.color.ohos_id_color_text_secondary'))
          .margin({ left: 4 })
      }
      .alignItems(VerticalAlign.Bottom)
      
      Blank()
      
      // 底部信息行
      Row() {
        // 趋势信息
        if (this.showTrend && this.data.trend !== undefined) {
          Row() {
            Image(this.getTrendIcon())
              .width(12)
              .height(12)
              .fillColor(this.getTrendColor())
            
            Text(`${Math.abs(this.data.trend).toFixed(1)}%`)
              .fontSize(fontSizes.trend)
              .fontColor(this.getTrendColor())
              .margin({ left: 2 })
          }
          .alignItems(VerticalAlign.Center)
        }
        
        Blank()
        
        // 最后更新时间
        if (this.showLastUpdate && this.data.lastUpdate) {
          Text(this.formatLastUpdate())
            .fontSize(fontSizes.trend)
            .fontColor($r('sys.color.ohos_id_color_text_secondary'))
        }
      }
      .width('100%')
      .alignItems(VerticalAlign.Center)
    }
    .width(dimensions.width)
    .height(dimensions.height)
    .padding(dimensions.padding)
    .backgroundColor(this.isPressed ? 
      $r('sys.color.ohos_id_color_click_effect') : 
      $r('sys.color.ohos_id_color_background_transparent'))
    .borderRadius(12)
    .border({
      width: 1,
      color: $r('sys.color.ohos_id_color_component_normal')
    })
    .shadow({
      radius: 4,
      color: $r('sys.color.ohos_id_color_shadow'),
      offsetX: 0,
      offsetY: 2
    })
    .animation({
      duration: 150,
      curve: Curve.EaseInOut
    })
    .gesture(
      TapGesture()
        .onAction(() => {
          if (this.clickable && this.onCardClick) {
            this.onCardClick(this.data);
          }
        })
    )
    .onTouch((event: TouchEvent) => {
      if (this.clickable) {
        switch (event.type) {
          case TouchType.Down:
            this.isPressed = true;
            break;
          case TouchType.Up:
          case TouchType.Cancel:
            this.isPressed = false;
            break;
        }
      }
    })
  }
}

/**
 * 监控卡片网格组件
 * 用于展示多个监控卡片
 */
@Component
export struct MonitorCardGrid {
  @Prop cards: MonitorData[];
  @Prop columns: number = 2;
  @Prop cardSize: CardSize = CardSize.MEDIUM;
  @Prop spacing: number = 12;
  
  onCardClick?: (data: MonitorData) => void;
  
  build() {
    Grid() {
      ForEach(this.cards, (card: MonitorData, index: number) => {
        GridItem() {
          MonitorCard({
            data: card,
            size: this.cardSize,
            onCardClick: this.onCardClick
          })
        }
      }, (card: MonitorData, index: number) => `${card.title}_${index}`)
    }
    .columnsTemplate(this.generateColumnsTemplate())
    .columnsGap(this.spacing)
    .rowsGap(this.spacing)
    .width('100%')
    .height('auto')
  }
  
  /**
   * 生成列模板
   */
  private generateColumnsTemplate(): string {
    return Array(this.columns).fill('1fr').join(' ');
  }
}

/**
 * 核心监控Agent
 * 继承原项目监控算法，适配鸿蒙硬件抽象层，实现跨设备数据采集
 */

import { BaseAgent, AgentStatus, StrategyPackage, AgentConfig } from '../../shared/agents/BaseAgent';
import { EventHub } from '../../shared/communication/EventHub';
import { HardwareAbstractionLayer, DeviceMetrics } from './HardwareAbstractionLayer';

export interface MonitoringConfig extends AgentConfig {
  samplingRate: number;        // 采样率 (Hz)
  enablePowerSaving: boolean;  // 启用功耗敏感模式
  thresholds: MonitoringThresholds;
  enableAnomalyDetection: boolean;
}

export interface MonitoringThresholds {
  cpu: { warning: number; critical: number };
  memory: { warning: number; critical: number };
  battery: { warning: number; critical: number };
  temperature: { warning: number; critical: number };
  storage: { warning: number; critical: number };
}

export interface AnomalyEvent {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  metric: string;
  value: number;
  threshold: number;
  timestamp: number;
  description: string;
}

export interface HealthScore {
  overall: number;
  components: {
    cpu: number;
    memory: number;
    battery: number;
    storage: number;
    network: number;
  };
  trend: number; // 变化趋势
}

export class MonitoringAgent extends BaseAgent {
  private hal: HardwareAbstractionLayer;
  private eventHub: EventHub;
  private config: MonitoringConfig;
  private isMonitoring: boolean = false;
  private monitoringTimer?: number;
  private lastMetrics?: DeviceMetrics;
  private healthHistory: HealthScore[] = [];
  private maxHistorySize: number = 100;
  
  constructor(config?: Partial<MonitoringConfig>) {
    const defaultConfig: MonitoringConfig = {
      agentId: 'monitoring-agent',
      samplingRate: 10, // 10Hz
      enablePowerSaving: true,
      enableAnomalyDetection: true,
      thresholds: {
        cpu: { warning: 70, critical: 90 },
        memory: { warning: 80, critical: 95 },
        battery: { warning: 20, critical: 10 },
        temperature: { warning: 60, critical: 80 },
        storage: { warning: 85, critical: 95 }
      },
      autoStart: false,
      retryCount: 3,
      retryDelay: 1000
    };
    
    super({ ...defaultConfig, ...config });
    this.config = { ...defaultConfig, ...config };
    this.hal = new HardwareAbstractionLayer();
    this.eventHub = EventHub.getInstance();
  }
  
  /**
   * 启动监控Agent
   */
  protected async onStart(): Promise<void> {
    console.info('MonitoringAgent: Starting...');
    
    // 初始化硬件抽象层
    await this.hal.initialize();
    
    // 订阅配置更新事件
    this.eventHub.subscribe('monitoring.config.update', (data) => {
      this.updateConfig(data.data);
    });
    
    // 订阅采样率调整事件
    this.eventHub.subscribe('monitoring.setSamplingRate', (data) => {
      this.setSamplingRate(data.data);
    });
    
    // 开始监控
    this.startMonitoring();
    
    console.info('MonitoringAgent: Started successfully');
  }
  
  /**
   * 停止监控Agent
   */
  protected async onStop(): Promise<void> {
    console.info('MonitoringAgent: Stopping...');
    
    this.stopMonitoring();
    
    console.info('MonitoringAgent: Stopped successfully');
  }
  
  /**
   * 开始监控循环
   */
  private startMonitoring(): void {
    if (this.isMonitoring) {
      console.warn('MonitoringAgent: Already monitoring');
      return;
    }
    
    this.isMonitoring = true;
    this.scheduleNextCollection();
    
    console.info(`MonitoringAgent: Started monitoring at ${this.config.samplingRate}Hz`);
  }
  
  /**
   * 停止监控循环
   */
  private stopMonitoring(): void {
    this.isMonitoring = false;
    
    if (this.monitoringTimer) {
      clearTimeout(this.monitoringTimer);
      this.monitoringTimer = undefined;
    }
    
    console.info('MonitoringAgent: Stopped monitoring');
  }
  
  /**
   * 调度下一次数据采集
   */
  private scheduleNextCollection(): void {
    if (!this.isMonitoring) return;
    
    const interval = 1000 / this.config.samplingRate; // 转换为毫秒
    
    this.monitoringTimer = setTimeout(async () => {
      try {
        await this.collectAndProcessMetrics();
      } catch (error) {
        console.error('MonitoringAgent: Error in monitoring loop:', error);
      } finally {
        this.scheduleNextCollection();
      }
    }, interval);
  }
  
  /**
   * 采集和处理指标数据
   */
  private async collectAndProcessMetrics(): Promise<void> {
    try {
      // 采集设备指标
      const metrics = await this.hal.getDeviceMetrics();
      
      // 计算健康度评分
      const healthScore = this.calculateHealthScore(metrics);
      
      // 检测异常
      const anomalies = this.detectAnomalies(metrics);
      
      // 发布监控数据事件
      this.eventHub.publish('monitoring.data', {
        metrics,
        healthScore,
        anomalies
      }, {
        source: this.agentId,
        priority: anomalies.length > 0 ? 2 : 1
      });
      
      // 如果检测到异常，发布异常事件
      if (anomalies.length > 0) {
        this.eventHub.publish('monitoring.anomaly', {
          anomalies,
          severity: this.calculateMaxSeverity(anomalies),
          metrics
        }, {
          source: this.agentId,
          priority: 3
        });
      }
      
      // 更新历史记录
      this.updateHistory(healthScore);
      this.lastMetrics = metrics;
      
      // 功耗优化检查
      if (this.config.enablePowerSaving) {
        this.checkPowerOptimization(metrics);
      }
      
    } catch (error) {
      console.error('MonitoringAgent: Failed to collect metrics:', error);
    }
  }
  
  /**
   * 计算设备健康度评分
   */
  private calculateHealthScore(metrics: DeviceMetrics): HealthScore {
    const weights = {
      cpu: 0.25,
      memory: 0.25,
      battery: 0.20,
      storage: 0.15,
      network: 0.15
    };
    
    // 计算各组件健康度
    const cpuScore = Math.max(0, 100 - metrics.cpu.usage);
    const memoryScore = Math.max(0, 100 - metrics.memory.usage);
    const batteryScore = metrics.battery.level;
    const storageScore = Math.max(0, 100 - metrics.storage.usage);
    const networkScore = metrics.network.isConnected ? 
      Math.min(100, metrics.network.signalStrength) : 0;
    
    // 计算总体健康度
    const overall = 
      cpuScore * weights.cpu +
      memoryScore * weights.memory +
      batteryScore * weights.battery +
      storageScore * weights.storage +
      networkScore * weights.network;
    
    // 计算趋势
    const trend = this.calculateHealthTrend(overall);
    
    return {
      overall: Math.round(overall),
      components: {
        cpu: Math.round(cpuScore),
        memory: Math.round(memoryScore),
        battery: Math.round(batteryScore),
        storage: Math.round(storageScore),
        network: Math.round(networkScore)
      },
      trend
    };
  }
  
  /**
   * 检测异常
   */
  private detectAnomalies(metrics: DeviceMetrics): AnomalyEvent[] {
    if (!this.config.enableAnomalyDetection) return [];
    
    const anomalies: AnomalyEvent[] = [];
    const thresholds = this.config.thresholds;
    
    // CPU异常检测
    if (metrics.cpu.usage >= thresholds.cpu.critical) {
      anomalies.push({
        type: 'cpu_critical',
        severity: 'critical',
        metric: 'cpu.usage',
        value: metrics.cpu.usage,
        threshold: thresholds.cpu.critical,
        timestamp: metrics.timestamp,
        description: `CPU使用率达到${metrics.cpu.usage.toFixed(1)}%，超过临界阈值${thresholds.cpu.critical}%`
      });
    } else if (metrics.cpu.usage >= thresholds.cpu.warning) {
      anomalies.push({
        type: 'cpu_warning',
        severity: 'medium',
        metric: 'cpu.usage',
        value: metrics.cpu.usage,
        threshold: thresholds.cpu.warning,
        timestamp: metrics.timestamp,
        description: `CPU使用率达到${metrics.cpu.usage.toFixed(1)}%，超过警告阈值${thresholds.cpu.warning}%`
      });
    }
    
    // 内存异常检测
    if (metrics.memory.usage >= thresholds.memory.critical) {
      anomalies.push({
        type: 'memory_critical',
        severity: 'critical',
        metric: 'memory.usage',
        value: metrics.memory.usage,
        threshold: thresholds.memory.critical,
        timestamp: metrics.timestamp,
        description: `内存使用率达到${metrics.memory.usage.toFixed(1)}%，超过临界阈值${thresholds.memory.critical}%`
      });
    } else if (metrics.memory.usage >= thresholds.memory.warning) {
      anomalies.push({
        type: 'memory_warning',
        severity: 'medium',
        metric: 'memory.usage',
        value: metrics.memory.usage,
        threshold: thresholds.memory.warning,
        timestamp: metrics.timestamp,
        description: `内存使用率达到${metrics.memory.usage.toFixed(1)}%，超过警告阈值${thresholds.memory.warning}%`
      });
    }
    
    // 电池异常检测
    if (metrics.battery.level <= thresholds.battery.critical) {
      anomalies.push({
        type: 'battery_critical',
        severity: 'critical',
        metric: 'battery.level',
        value: metrics.battery.level,
        threshold: thresholds.battery.critical,
        timestamp: metrics.timestamp,
        description: `电池电量降至${metrics.battery.level}%，低于临界阈值${thresholds.battery.critical}%`
      });
    } else if (metrics.battery.level <= thresholds.battery.warning) {
      anomalies.push({
        type: 'battery_warning',
        severity: 'medium',
        metric: 'battery.level',
        value: metrics.battery.level,
        threshold: thresholds.battery.warning,
        timestamp: metrics.timestamp,
        description: `电池电量降至${metrics.battery.level}%，低于警告阈值${thresholds.battery.warning}%`
      });
    }
    
    // 存储异常检测
    if (metrics.storage.usage >= thresholds.storage.critical) {
      anomalies.push({
        type: 'storage_critical',
        severity: 'critical',
        metric: 'storage.usage',
        value: metrics.storage.usage,
        threshold: thresholds.storage.critical,
        timestamp: metrics.timestamp,
        description: `存储使用率达到${metrics.storage.usage.toFixed(1)}%，超过临界阈值${thresholds.storage.critical}%`
      });
    } else if (metrics.storage.usage >= thresholds.storage.warning) {
      anomalies.push({
        type: 'storage_warning',
        severity: 'medium',
        metric: 'storage.usage',
        value: metrics.storage.usage,
        threshold: thresholds.storage.warning,
        timestamp: metrics.timestamp,
        description: `存储使用率达到${metrics.storage.usage.toFixed(1)}%，超过警告阈值${thresholds.storage.warning}%`
      });
    }
    
    // 温度异常检测
    const maxTemp = Math.max(metrics.temperature.cpu, metrics.temperature.battery);
    if (maxTemp >= thresholds.temperature.critical) {
      anomalies.push({
        type: 'temperature_critical',
        severity: 'critical',
        metric: 'temperature.max',
        value: maxTemp,
        threshold: thresholds.temperature.critical,
        timestamp: metrics.timestamp,
        description: `设备温度达到${maxTemp.toFixed(1)}°C，超过临界阈值${thresholds.temperature.critical}°C`
      });
    } else if (maxTemp >= thresholds.temperature.warning) {
      anomalies.push({
        type: 'temperature_warning',
        severity: 'medium',
        metric: 'temperature.max',
        value: maxTemp,
        threshold: thresholds.temperature.warning,
        timestamp: metrics.timestamp,
        description: `设备温度达到${maxTemp.toFixed(1)}°C，超过警告阈值${thresholds.temperature.warning}°C`
      });
    }
    
    return anomalies;
  }
  
  /**
   * 计算健康度趋势
   */
  private calculateHealthTrend(currentScore: number): number {
    if (this.healthHistory.length < 2) return 0;
    
    const recentHistory = this.healthHistory.slice(-10); // 最近10次记录
    const oldAverage = recentHistory.slice(0, -1).reduce((sum, h) => sum + h.overall, 0) / (recentHistory.length - 1);
    
    return currentScore - oldAverage;
  }
  
  /**
   * 计算最大异常严重程度
   */
  private calculateMaxSeverity(anomalies: AnomalyEvent[]): string {
    const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 };
    let maxLevel = 0;
    let maxSeverity = 'low';
    
    anomalies.forEach(anomaly => {
      const level = severityLevels[anomaly.severity];
      if (level > maxLevel) {
        maxLevel = level;
        maxSeverity = anomaly.severity;
      }
    });
    
    return maxSeverity;
  }
  
  /**
   * 更新健康度历史
   */
  private updateHistory(healthScore: HealthScore): void {
    this.healthHistory.push(healthScore);
    
    if (this.healthHistory.length > this.maxHistorySize) {
      this.healthHistory = this.healthHistory.slice(-this.maxHistorySize);
    }
  }
  
  /**
   * 功耗优化检查
   */
  private checkPowerOptimization(metrics: DeviceMetrics): void {
    if (metrics.battery.level < 20) {
      // 进入省电模式
      this.setSamplingRate(1); // 降低到1Hz
      this.eventHub.publish('power.enterSavingMode', {
        batteryLevel: metrics.battery.level,
        reason: 'low_battery'
      });
    } else if (metrics.battery.level < 50 && this.config.samplingRate > 5) {
      // 进入平衡模式
      this.setSamplingRate(5); // 降低到5Hz
    }
  }
  
  /**
   * 设置采样率
   */
  setSamplingRate(rate: number): void {
    if (rate < 1 || rate > 60) {
      console.warn(`MonitoringAgent: Invalid sampling rate ${rate}, must be between 1-60Hz`);
      return;
    }
    
    this.config.samplingRate = rate;
    console.info(`MonitoringAgent: Sampling rate updated to ${rate}Hz`);
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.info('MonitoringAgent: Configuration updated');
  }
  
  /**
   * 获取当前指标
   */
  getCurrentMetrics(): DeviceMetrics | undefined {
    return this.lastMetrics;
  }
  
  /**
   * 获取健康度历史
   */
  getHealthHistory(): HealthScore[] {
    return [...this.healthHistory];
  }
  
  /**
   * 热更新策略
   */
  protected async loadStrategy(strategy: StrategyPackage): Promise<void> {
    console.info('MonitoringAgent: Loading new strategy package');
    
    // 更新阈值配置
    if (strategy.strategies.has('thresholds')) {
      this.config.thresholds = strategy.strategies.get('thresholds');
    }
    
    // 更新采样率
    if (strategy.strategies.has('samplingRate')) {
      this.setSamplingRate(strategy.strategies.get('samplingRate'));
    }
    
    // 更新异常检测配置
    if (strategy.strategies.has('anomalyDetection')) {
      this.config.enableAnomalyDetection = strategy.strategies.get('anomalyDetection');
    }
  }
  
  /**
   * 创建备份
   */
  protected async createBackup(): Promise<any> {
    return {
      config: { ...this.config },
      healthHistory: [...this.healthHistory]
    };
  }
  
  /**
   * 从备份恢复
   */
  protected async restoreFromBackup(backup: any): Promise<void> {
    this.config = backup.config;
    this.healthHistory = backup.healthHistory;
  }
}

/**
 * RPC管理器
 * 负责Agent间的远程过程调用
 */

export interface RPCRequest {
  id: string;
  method: string;
  params: any[];
  timestamp: number;
  timeout?: number;
}

export interface RPCResponse {
  id: string;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
  timestamp: number;
}

export interface RPCHandler {
  (params: any[]): Promise<any>;
}

export class RPCManager {
  private static instance: RPCManager;
  private handlers: Map<string, RPCHandler> = new Map();
  private pendingRequests: Map<string, {
    resolve: (value: any) => void;
    reject: (reason: any) => void;
    timeout: number;
  }> = new Map();
  private defaultTimeout: number = 30000; // 30秒
  
  private constructor() {}
  
  static getInstance(): RPCManager {
    if (!RPCManager.instance) {
      RPCManager.instance = new RPCManager();
    }
    return RPCManager.instance;
  }
  
  /**
   * 注册RPC方法处理器
   */
  register(method: string, handler: RPCHandler): void {
    this.handlers.set(method, handler);
    console.debug(`RPC: Registered method '${method}'`);
  }
  
  /**
   * 取消注册RPC方法
   */
  unregister(method: string): boolean {
    const result = this.handlers.delete(method);
    if (result) {
      console.debug(`RPC: Unregistered method '${method}'`);
    }
    return result;
  }
  
  /**
   * 调用RPC方法
   */
  async call(method: string, params: any[] = [], timeout?: number): Promise<any> {
    const request: RPCRequest = {
      id: this.generateRequestId(),
      method,
      params,
      timestamp: Date.now(),
      timeout: timeout || this.defaultTimeout
    };
    
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(request.id);
        reject(new Error(`RPC call '${method}' timed out after ${request.timeout}ms`));
      }, request.timeout);
      
      // 存储待处理请求
      this.pendingRequests.set(request.id, {
        resolve: (value) => {
          clearTimeout(timeoutId);
          resolve(value);
        },
        reject: (reason) => {
          clearTimeout(timeoutId);
          reject(reason);
        },
        timeout: timeoutId
      });
      
      // 处理请求
      this.processRequest(request);
    });
  }
  
  /**
   * 处理RPC请求
   */
  private async processRequest(request: RPCRequest): Promise<void> {
    try {
      const handler = this.handlers.get(request.method);
      
      if (!handler) {
        this.sendResponse({
          id: request.id,
          error: {
            code: -32601,
            message: `Method '${request.method}' not found`
          },
          timestamp: Date.now()
        });
        return;
      }
      
      // 执行处理器
      const result = await handler(request.params);
      
      // 发送成功响应
      this.sendResponse({
        id: request.id,
        result,
        timestamp: Date.now()
      });
      
    } catch (error) {
      // 发送错误响应
      this.sendResponse({
        id: request.id,
        error: {
          code: -32603,
          message: error instanceof Error ? error.message : 'Internal error',
          data: error
        },
        timestamp: Date.now()
      });
    }
  }
  
  /**
   * 发送RPC响应
   */
  private sendResponse(response: RPCResponse): void {
    const pendingRequest = this.pendingRequests.get(response.id);
    
    if (pendingRequest) {
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        const error = new Error(response.error.message);
        (error as any).code = response.error.code;
        (error as any).data = response.error.data;
        pendingRequest.reject(error);
      } else {
        pendingRequest.resolve(response.result);
      }
    }
  }
  
  /**
   * 批量调用RPC方法
   */
  async batchCall(calls: Array<{
    method: string;
    params?: any[];
    timeout?: number;
  }>): Promise<any[]> {
    const promises = calls.map(call => 
      this.call(call.method, call.params, call.timeout)
        .catch(error => ({ error }))
    );
    
    return Promise.all(promises);
  }
  
  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `rpc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 获取统计信息
   */
  getStats(): Record<string, any> {
    return {
      registeredMethods: Array.from(this.handlers.keys()),
      pendingRequests: this.pendingRequests.size,
      defaultTimeout: this.defaultTimeout
    };
  }
  
  /**
   * 清理超时的请求
   */
  cleanup(): void {
    const now = Date.now();
    for (const [id, request] of this.pendingRequests.entries()) {
      // 这里需要检查请求是否超时，但由于我们使用setTimeout，
      // 超时的请求应该已经被自动清理了
    }
  }
}

/**
 * RPC装饰器 - 用于简化RPC方法注册
 */
export function RPCMethod(method: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    // 在类实例化时注册RPC方法
    const rpcManager = RPCManager.getInstance();
    rpcManager.register(method, async (params: any[]) => {
      return originalMethod.apply(target, params);
    });
    
    return descriptor;
  };
}

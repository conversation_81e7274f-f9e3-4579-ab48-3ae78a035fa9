/**
 * 基础Agent抽象类
 * 定义所有Agent的通用接口和行为
 */

export enum AgentStatus {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  ERROR = 'error'
}

export interface StrategyPackage {
  version: string;
  strategies: Map<string, any>;
  metadata: Record<string, any>;
}

export interface AgentConfig {
  agentId: string;
  autoStart?: boolean;
  retryCount?: number;
  retryDelay?: number;
}

export abstract class BaseAgent {
  protected agentId: string;
  protected status: AgentStatus;
  protected config: AgentConfig;
  protected startTime?: number;
  protected lastHeartbeat?: number;
  
  constructor(config: AgentConfig) {
    this.agentId = config.agentId;
    this.status = AgentStatus.STOPPED;
    this.config = {
      autoStart: false,
      retryCount: 3,
      retryDelay: 1000,
      ...config
    };
  }
  
  /**
   * 启动Agent
   */
  async start(): Promise<void> {
    if (this.status === AgentStatus.RUNNING) {
      console.warn(`Agent ${this.agentId} is already running`);
      return;
    }
    
    this.status = AgentStatus.STARTING;
    this.startTime = Date.now();
    
    try {
      await this.onStart();
      this.status = AgentStatus.RUNNING;
      this.startHeartbeat();
      console.info(`Agent ${this.agentId} started successfully`);
    } catch (error) {
      this.status = AgentStatus.ERROR;
      console.error(`Agent ${this.agentId} start failed:`, error);
      throw error;
    }
  }
  
  /**
   * 停止Agent
   */
  async stop(): Promise<void> {
    if (this.status === AgentStatus.STOPPED) {
      console.warn(`Agent ${this.agentId} is already stopped`);
      return;
    }
    
    this.status = AgentStatus.STOPPING;
    
    try {
      this.stopHeartbeat();
      await this.onStop();
      this.status = AgentStatus.STOPPED;
      console.info(`Agent ${this.agentId} stopped successfully`);
    } catch (error) {
      this.status = AgentStatus.ERROR;
      console.error(`Agent ${this.agentId} stop failed:`, error);
      throw error;
    }
  }
  
  /**
   * 重启Agent
   */
  async restart(): Promise<void> {
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
    await this.start();
  }
  
  /**
   * 热更新接口
   */
  async hotUpdate(strategyPackage: StrategyPackage): Promise<boolean> {
    try {
      console.info(`Agent ${this.agentId} starting hot update to version ${strategyPackage.version}`);
      
      // 验证策略包
      if (!this.validateStrategyPackage(strategyPackage)) {
        throw new Error('Invalid strategy package');
      }
      
      // 备份当前状态
      const backup = await this.createBackup();
      
      try {
        // 加载新策略
        await this.loadStrategy(strategyPackage);
        console.info(`Agent ${this.agentId} hot update completed successfully`);
        return true;
      } catch (error) {
        // 回滚到备份状态
        await this.restoreFromBackup(backup);
        throw error;
      }
    } catch (error) {
      console.error(`Agent ${this.agentId} hot update failed:`, error);
      return false;
    }
  }
  
  /**
   * 获取Agent状态信息
   */
  getStatus(): AgentStatus {
    return this.status;
  }
  
  /**
   * 获取Agent运行时信息
   */
  getRuntimeInfo(): Record<string, any> {
    return {
      agentId: this.agentId,
      status: this.status,
      startTime: this.startTime,
      uptime: this.startTime ? Date.now() - this.startTime : 0,
      lastHeartbeat: this.lastHeartbeat,
      config: this.config
    };
  }
  
  /**
   * 心跳检测
   */
  private startHeartbeat(): void {
    setInterval(() => {
      this.lastHeartbeat = Date.now();
      this.onHeartbeat();
    }, 30000); // 30秒心跳间隔
  }
  
  private stopHeartbeat(): void {
    // 清理心跳定时器的逻辑
  }
  
  /**
   * 抽象方法 - 子类必须实现
   */
  protected abstract onStart(): Promise<void>;
  protected abstract onStop(): Promise<void>;
  protected abstract loadStrategy(strategy: StrategyPackage): Promise<void>;
  protected abstract createBackup(): Promise<any>;
  protected abstract restoreFromBackup(backup: any): Promise<void>;
  
  /**
   * 可选重写的方法
   */
  protected onHeartbeat(): void {
    // 默认心跳处理
  }
  
  protected validateStrategyPackage(strategyPackage: StrategyPackage): boolean {
    return strategyPackage && 
           strategyPackage.version && 
           strategyPackage.strategies instanceof Map;
  }
}

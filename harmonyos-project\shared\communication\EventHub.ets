/**
 * EventHub - 事件中心
 * 负责Agent间的事件通信和跨设备事件分发
 */

import distributedDeviceManager from '@ohos.distributedDeviceManager';
import { BusinessError } from '@ohos.base';

export interface EventData {
  eventId: string;
  timestamp: number;
  source: string;
  data: any;
  priority?: EventPriority;
}

export enum EventPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

export interface EventSubscription {
  id: string;
  event: string;
  callback: (data: EventData) => void;
  filter?: (data: EventData) => boolean;
  once?: boolean;
}

export class EventHub {
  private static instance: EventHub;
  private subscribers: Map<string, EventSubscription[]> = new Map();
  private eventHistory: EventData[] = [];
  private maxHistorySize: number = 1000;
  private deviceManager?: distributedDeviceManager.DeviceManager;
  
  private constructor() {
    this.initializeDistributedCapability();
  }
  
  static getInstance(): EventHub {
    if (!EventHub.instance) {
      EventHub.instance = new EventHub();
    }
    return EventHub.instance;
  }
  
  /**
   * 初始化分布式能力
   */
  private async initializeDistributedCapability(): Promise<void> {
    try {
      this.deviceManager = distributedDeviceManager.createDeviceManager('com.goofish.monitor');
      console.info('EventHub: Distributed capability initialized');
    } catch (error) {
      console.error('EventHub: Failed to initialize distributed capability:', error);
    }
  }
  
  /**
   * 订阅事件
   */
  subscribe(event: string, callback: (data: EventData) => void, options?: {
    filter?: (data: EventData) => boolean;
    once?: boolean;
  }): string {
    const subscriptionId = this.generateSubscriptionId();
    const subscription: EventSubscription = {
      id: subscriptionId,
      event,
      callback,
      filter: options?.filter,
      once: options?.once
    };
    
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, []);
    }
    
    this.subscribers.get(event)!.push(subscription);
    console.debug(`EventHub: Subscribed to event '${event}' with ID ${subscriptionId}`);
    
    return subscriptionId;
  }
  
  /**
   * 取消订阅
   */
  unsubscribe(subscriptionId: string): boolean {
    for (const [event, subscriptions] of this.subscribers.entries()) {
      const index = subscriptions.findIndex(sub => sub.id === subscriptionId);
      if (index !== -1) {
        subscriptions.splice(index, 1);
        if (subscriptions.length === 0) {
          this.subscribers.delete(event);
        }
        console.debug(`EventHub: Unsubscribed from event with ID ${subscriptionId}`);
        return true;
      }
    }
    return false;
  }
  
  /**
   * 发布事件
   */
  publish(event: string, data: any, options?: {
    source?: string;
    priority?: EventPriority;
  }): void {
    const eventData: EventData = {
      eventId: this.generateEventId(),
      timestamp: Date.now(),
      source: options?.source || 'unknown',
      data,
      priority: options?.priority || EventPriority.NORMAL
    };
    
    // 添加到历史记录
    this.addToHistory(eventData);
    
    // 获取订阅者
    const subscriptions = this.subscribers.get(event) || [];
    const toRemove: string[] = [];
    
    // 通知订阅者
    subscriptions.forEach(subscription => {
      try {
        // 应用过滤器
        if (subscription.filter && !subscription.filter(eventData)) {
          return;
        }
        
        // 执行回调
        subscription.callback(eventData);
        
        // 标记一次性订阅待删除
        if (subscription.once) {
          toRemove.push(subscription.id);
        }
      } catch (error) {
        console.error(`EventHub: Error in event callback for ${event}:`, error);
      }
    });
    
    // 清理一次性订阅
    toRemove.forEach(id => this.unsubscribe(id));
    
    console.debug(`EventHub: Published event '${event}' to ${subscriptions.length} subscribers`);
  }
  
  /**
   * 跨设备事件分发
   */
  async publishDistributed(event: string, data: any, options?: {
    targetDevices?: string[];
    source?: string;
    priority?: EventPriority;
  }): Promise<void> {
    if (!this.deviceManager) {
      console.warn('EventHub: Distributed capability not available');
      return;
    }
    
    const eventData: EventData = {
      eventId: this.generateEventId(),
      timestamp: Date.now(),
      source: options?.source || 'local',
      data,
      priority: options?.priority || EventPriority.NORMAL
    };
    
    try {
      // 获取可用设备列表
      const deviceList = this.deviceManager.getAvailableDeviceListSync();
      const targetDevices = options?.targetDevices || deviceList.map(device => device.deviceId);
      
      // 发送到目标设备
      for (const deviceId of targetDevices) {
        await this.sendToDevice(deviceId, event, eventData);
      }
      
      console.info(`EventHub: Distributed event '${event}' to ${targetDevices.length} devices`);
    } catch (error) {
      console.error('EventHub: Failed to publish distributed event:', error);
    }
  }
  
  /**
   * 发送事件到指定设备
   */
  private async sendToDevice(deviceId: string, event: string, eventData: EventData): Promise<void> {
    // 这里需要实现具体的跨设备通信逻辑
    // 可以使用鸿蒙的分布式数据管理或RPC能力
    console.debug(`EventHub: Sending event '${event}' to device ${deviceId}`);
  }
  
  /**
   * 获取事件历史
   */
  getEventHistory(filter?: {
    event?: string;
    source?: string;
    startTime?: number;
    endTime?: number;
  }): EventData[] {
    let history = [...this.eventHistory];
    
    if (filter) {
      history = history.filter(eventData => {
        if (filter.event && !eventData.eventId.includes(filter.event)) return false;
        if (filter.source && eventData.source !== filter.source) return false;
        if (filter.startTime && eventData.timestamp < filter.startTime) return false;
        if (filter.endTime && eventData.timestamp > filter.endTime) return false;
        return true;
      });
    }
    
    return history;
  }
  
  /**
   * 清理事件历史
   */
  clearHistory(): void {
    this.eventHistory = [];
    console.info('EventHub: Event history cleared');
  }
  
  /**
   * 添加事件到历史记录
   */
  private addToHistory(eventData: EventData): void {
    this.eventHistory.push(eventData);
    
    // 限制历史记录大小
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }
  
  /**
   * 生成订阅ID
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * 获取统计信息
   */
  getStats(): Record<string, any> {
    const eventCounts: Record<string, number> = {};
    
    for (const [event, subscriptions] of this.subscribers.entries()) {
      eventCounts[event] = subscriptions.length;
    }
    
    return {
      totalSubscriptions: Array.from(this.subscribers.values()).reduce((sum, subs) => sum + subs.length, 0),
      eventTypes: this.subscribers.size,
      eventCounts,
      historySize: this.eventHistory.length,
      distributedEnabled: !!this.deviceManager
    };
  }
}

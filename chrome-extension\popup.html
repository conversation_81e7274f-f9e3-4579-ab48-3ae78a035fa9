<!DOCTYPE html>
<html>
<meta charset="UTF-8">
<head>
  <style>
    body {
      width: 400px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    #stateOutput {
      width: 100%;
      height: 300px;
      font-family: monospace;
      font-size: 12px;
      white-space: pre-wrap;
      overflow-y: auto;
    }
    button {
      margin: 10px 0;
      padding: 10px;
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
      width: 100%;
    }
    button:hover {
      background-color: #45a049;
    }
    .status {
      margin: 10px 0;
      padding: 10px;
      border-radius: 4px;
    }
    .success {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .error {
      background-color: #f2dede;
      color: #a94442;
    }
  </style>
</head>
<body>
  <h2>Xianyu Login State Extractor</h2>
  <button id="extractBtn">1.点击获取登录状态</button>
  <div id="status"></div>
  <textarea id="stateOutput" readonly></textarea>
  <button id="copyBtn">2.点击复制</button>

  <script src="popup.js"></script>
</body>
</html>
{"module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ts", "description": "$string:EntryAbility_desc", "icon": "$media:icon", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:icon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}]}], "extensionAbilities": [{"name": "MonitorFormExtensionAbility", "srcEntry": "./ets/atomicservices/MonitorFormExtensionAbility.ts", "type": "form", "exported": true, "metadata": [{"name": "ohos.extension.form", "resource": "$profile:form_config"}]}], "requestPermissions": [{"name": "ohos.permission.DISTRIBUTED_DATASYNC", "reason": "$string:permission_distributed_datasync", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}, {"name": "ohos.permission.GET_NETWORK_INFO", "reason": "$string:permission_network_info", "usedScene": {"abilities": ["EntryAbility"], "when": "always"}}, {"name": "ohos.permission.VIBRATE", "reason": "$string:permission_vibrate", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}, {"name": "ohos.permission.NOTIFICATION_CONTROLLER", "reason": "$string:permission_notification", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}]}}
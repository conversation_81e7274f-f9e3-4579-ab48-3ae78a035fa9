{"forms": [{"name": "monitor_overview", "description": "设备监控概览卡片", "src": "./ets/atomicservices/MonitorCard.ets", "uiSyntax": "arkts", "window": {"designWidth": 720, "autoDesignWidth": true}, "colorMode": "auto", "isDefault": true, "updateEnabled": true, "scheduledUpdateTime": "10:30", "updateDuration": 1, "defaultDimension": "2*2", "supportDimensions": ["2*2", "4*4"], "metaData": {"customizeData": [{"name": "originWidgetName", "value": "monitor_overview"}]}}, {"name": "health_score", "description": "设备健康度卡片", "src": "./ets/atomicservices/MonitorCard.ets", "uiSyntax": "arkts", "window": {"designWidth": 720, "autoDesignWidth": true}, "colorMode": "auto", "isDefault": false, "updateEnabled": true, "scheduledUpdateTime": "10:30", "updateDuration": 2, "defaultDimension": "2*2", "supportDimensions": ["1*2", "2*2"], "metaData": {"customizeData": [{"name": "originWidgetName", "value": "health_score"}]}}, {"name": "quick_status", "description": "快速状态卡片", "src": "./ets/atomicservices/MonitorCard.ets", "uiSyntax": "arkts", "window": {"designWidth": 720, "autoDesignWidth": true}, "colorMode": "auto", "isDefault": false, "updateEnabled": true, "scheduledUpdateTime": "10:30", "updateDuration": 1, "defaultDimension": "2*1", "supportDimensions": ["2*1", "4*1"], "metaData": {"customizeData": [{"name": "originWidgetName", "value": "quick_status"}]}}]}
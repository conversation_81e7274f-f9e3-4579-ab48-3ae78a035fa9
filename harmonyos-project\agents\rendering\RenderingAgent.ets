/**
 * 前端数据渲染Agent
 * 分离UI与数据处理逻辑，作为独立Agent接收监控数据流并驱动视图更新
 */

import { BaseAgent, AgentStatus, StrategyPackage, AgentConfig } from '../../shared/agents/BaseAgent';
import { EventHub } from '../../shared/communication/EventHub';
import { DeviceMetrics } from '../monitoring/HardwareAbstractionLayer';
import { HealthScore, AnomalyEvent } from '../monitoring/MonitoringAgent';

export interface RenderingConfig extends AgentConfig {
  updateInterval: number;      // UI更新间隔 (ms)
  enableAnimations: boolean;   // 启用动画
  maxDataPoints: number;       // 最大数据点数
  enableHighlighting: boolean; // 启用异常高亮
  deviceAdaptation: boolean;   // 启用设备适配
}

export interface VisualData {
  type: 'chart' | 'card' | 'gauge' | 'list';
  id: string;
  title: string;
  data: any;
  config: VisualConfig;
  timestamp: number;
}

export interface VisualConfig {
  colors?: string[];
  animation?: AnimationConfig;
  layout?: LayoutConfig;
  highlighting?: HighlightConfig;
}

export interface AnimationConfig {
  enabled: boolean;
  duration: number;
  easing: string;
}

export interface LayoutConfig {
  width?: number | string;
  height?: number | string;
  margin?: { top: number; right: number; bottom: number; left: number };
}

export interface HighlightConfig {
  enabled: boolean;
  color: string;
  intensity: number;
  duration: number;
}

export interface DeviceAdapter {
  deviceType: string;
  screenSize: { width: number; height: number };
  adaptationRules: AdaptationRule[];
}

export interface AdaptationRule {
  condition: string;
  action: string;
  parameters: Record<string, any>;
}

export class RenderingAgent extends BaseAgent {
  private eventHub: EventHub;
  private config: RenderingConfig;
  private renderPipeline: RenderPipeline;
  private deviceAdapter: DeviceAdapterManager;
  private dataBuffer: Map<string, any[]> = new Map();
  private visualElements: Map<string, VisualData> = new Map();
  private updateTimer?: number;
  private isRendering: boolean = false;
  
  constructor(config?: Partial<RenderingConfig>) {
    const defaultConfig: RenderingConfig = {
      agentId: 'rendering-agent',
      updateInterval: 1000, // 1秒更新间隔
      enableAnimations: true,
      maxDataPoints: 100,
      enableHighlighting: true,
      deviceAdaptation: true,
      autoStart: false,
      retryCount: 3,
      retryDelay: 1000
    };
    
    super({ ...defaultConfig, ...config });
    this.config = { ...defaultConfig, ...config };
    this.eventHub = EventHub.getInstance();
    this.renderPipeline = new RenderPipeline(this.config);
    this.deviceAdapter = new DeviceAdapterManager();
  }
  
  /**
   * 启动渲染Agent
   */
  protected async onStart(): Promise<void> {
    console.info('RenderingAgent: Starting...');
    
    // 初始化设备适配器
    await this.deviceAdapter.initialize();
    
    // 订阅监控数据事件
    this.eventHub.subscribe('monitoring.data', (eventData) => {
      this.processMonitoringData(eventData.data);
    });
    
    // 订阅异常数据事件
    this.eventHub.subscribe('monitoring.anomaly', (eventData) => {
      this.processAnomalyData(eventData.data);
    });
    
    // 订阅UI更新配置事件
    this.eventHub.subscribe('rendering.setUpdateInterval', (eventData) => {
      this.setUpdateInterval(eventData.data);
    });
    
    // 订阅设备变化事件
    this.eventHub.subscribe('device.changed', (eventData) => {
      this.handleDeviceChange(eventData.data);
    });
    
    // 开始渲染循环
    this.startRenderingLoop();
    
    console.info('RenderingAgent: Started successfully');
  }
  
  /**
   * 停止渲染Agent
   */
  protected async onStop(): Promise<void> {
    console.info('RenderingAgent: Stopping...');
    
    this.stopRenderingLoop();
    
    console.info('RenderingAgent: Stopped successfully');
  }
  
  /**
   * 处理监控数据
   */
  private async processMonitoringData(data: {
    metrics: DeviceMetrics;
    healthScore: HealthScore;
    anomalies: AnomalyEvent[];
  }): Promise<void> {
    try {
      // 更新数据缓冲区
      this.updateDataBuffer('metrics', data.metrics);
      this.updateDataBuffer('healthScore', data.healthScore);
      
      // 生成可视化数据
      const visualData = await this.renderPipeline.processMetrics(data.metrics, data.healthScore);
      
      // 应用设备适配
      const adaptedData = await this.deviceAdapter.adaptVisualData(visualData);
      
      // 存储可视化元素
      adaptedData.forEach(visual => {
        this.visualElements.set(visual.id, visual);
      });
      
      console.debug(`RenderingAgent: Processed monitoring data, generated ${adaptedData.length} visual elements`);
    } catch (error) {
      console.error('RenderingAgent: Failed to process monitoring data:', error);
    }
  }
  
  /**
   * 处理异常数据
   */
  private async processAnomalyData(data: {
    anomalies: AnomalyEvent[];
    severity: string;
    metrics: DeviceMetrics;
  }): Promise<void> {
    if (!this.config.enableHighlighting) return;
    
    try {
      // 生成异常高亮配置
      const highlightConfigs = this.generateHighlightConfigs(data.anomalies, data.severity);
      
      // 应用高亮效果
      for (const config of highlightConfigs) {
        await this.applyHighlight(config);
      }
      
      // 发布UI高亮事件
      this.eventHub.publish('ui.highlight', {
        configs: highlightConfigs,
        severity: data.severity,
        timestamp: Date.now()
      }, {
        source: this.agentId,
        priority: 2
      });
      
      console.info(`RenderingAgent: Applied ${highlightConfigs.length} highlight effects for ${data.severity} anomalies`);
    } catch (error) {
      console.error('RenderingAgent: Failed to process anomaly data:', error);
    }
  }
  
  /**
   * 开始渲染循环
   */
  private startRenderingLoop(): void {
    if (this.isRendering) {
      console.warn('RenderingAgent: Already rendering');
      return;
    }
    
    this.isRendering = true;
    this.scheduleNextRender();
    
    console.info(`RenderingAgent: Started rendering loop with ${this.config.updateInterval}ms interval`);
  }
  
  /**
   * 停止渲染循环
   */
  private stopRenderingLoop(): void {
    this.isRendering = false;
    
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = undefined;
    }
    
    console.info('RenderingAgent: Stopped rendering loop');
  }
  
  /**
   * 调度下一次渲染
   */
  private scheduleNextRender(): void {
    if (!this.isRendering) return;
    
    this.updateTimer = setTimeout(async () => {
      try {
        await this.performRender();
      } catch (error) {
        console.error('RenderingAgent: Error in rendering loop:', error);
      } finally {
        this.scheduleNextRender();
      }
    }, this.config.updateInterval);
  }
  
  /**
   * 执行渲染
   */
  private async performRender(): Promise<void> {
    if (this.visualElements.size === 0) return;
    
    // 收集所有可视化元素
    const visualData = Array.from(this.visualElements.values());
    
    // 发布UI更新事件
    this.eventHub.publish('ui.update', {
      visualData,
      timestamp: Date.now()
    }, {
      source: this.agentId,
      priority: 1
    });
    
    console.debug(`RenderingAgent: Rendered ${visualData.length} visual elements`);
  }
  
  /**
   * 更新数据缓冲区
   */
  private updateDataBuffer(key: string, data: any): void {
    if (!this.dataBuffer.has(key)) {
      this.dataBuffer.set(key, []);
    }
    
    const buffer = this.dataBuffer.get(key)!;
    buffer.push({
      data,
      timestamp: Date.now()
    });
    
    // 限制缓冲区大小
    if (buffer.length > this.config.maxDataPoints) {
      buffer.splice(0, buffer.length - this.config.maxDataPoints);
    }
  }
  
  /**
   * 生成高亮配置
   */
  private generateHighlightConfigs(anomalies: AnomalyEvent[], severity: string): HighlightConfig[] {
    const configs: HighlightConfig[] = [];
    
    const colorMap = {
      low: '#FFA500',      // 橙色
      medium: '#FF6B35',   // 橙红色
      high: '#FF4444',     // 红色
      critical: '#CC0000'  // 深红色
    };
    
    const intensityMap = {
      low: 0.3,
      medium: 0.5,
      high: 0.7,
      critical: 1.0
    };
    
    anomalies.forEach(anomaly => {
      configs.push({
        enabled: true,
        color: colorMap[anomaly.severity] || colorMap.medium,
        intensity: intensityMap[anomaly.severity] || intensityMap.medium,
        duration: anomaly.severity === 'critical' ? 5000 : 3000
      });
    });
    
    return configs;
  }
  
  /**
   * 应用高亮效果
   */
  private async applyHighlight(config: HighlightConfig): Promise<void> {
    // 这里实现具体的高亮效果逻辑
    // 可以通过修改可视化元素的样式来实现
    console.debug('RenderingAgent: Applied highlight effect', config);
  }
  
  /**
   * 处理设备变化
   */
  private async handleDeviceChange(deviceInfo: any): Promise<void> {
    if (!this.config.deviceAdaptation) return;
    
    try {
      // 更新设备适配器
      await this.deviceAdapter.updateDevice(deviceInfo);
      
      // 重新适配所有可视化元素
      const visualData = Array.from(this.visualElements.values());
      const adaptedData = await this.deviceAdapter.adaptVisualData(visualData);
      
      // 更新可视化元素
      adaptedData.forEach(visual => {
        this.visualElements.set(visual.id, visual);
      });
      
      console.info('RenderingAgent: Adapted to device change');
    } catch (error) {
      console.error('RenderingAgent: Failed to handle device change:', error);
    }
  }
  
  /**
   * 设置更新间隔
   */
  setUpdateInterval(interval: number): void {
    if (interval < 100 || interval > 10000) {
      console.warn(`RenderingAgent: Invalid update interval ${interval}, must be between 100-10000ms`);
      return;
    }
    
    this.config.updateInterval = interval;
    console.info(`RenderingAgent: Update interval set to ${interval}ms`);
  }
  
  /**
   * 获取可视化数据
   */
  getVisualData(): VisualData[] {
    return Array.from(this.visualElements.values());
  }
  
  /**
   * 获取数据缓冲区
   */
  getDataBuffer(key?: string): Map<string, any[]> | any[] {
    if (key) {
      return this.dataBuffer.get(key) || [];
    }
    return this.dataBuffer;
  }
  
  /**
   * 清理过期数据
   */
  cleanupExpiredData(): void {
    const now = Date.now();
    const maxAge = 300000; // 5分钟
    
    for (const [key, buffer] of this.dataBuffer.entries()) {
      const validData = buffer.filter(item => now - item.timestamp < maxAge);
      this.dataBuffer.set(key, validData);
    }
  }
  
  /**
   * 热更新策略
   */
  protected async loadStrategy(strategy: StrategyPackage): Promise<void> {
    console.info('RenderingAgent: Loading new strategy package');
    
    // 更新渲染配置
    if (strategy.strategies.has('renderingConfig')) {
      const newConfig = strategy.strategies.get('renderingConfig');
      this.config = { ...this.config, ...newConfig };
    }
    
    // 更新渲染管道
    if (strategy.strategies.has('renderPipeline')) {
      await this.renderPipeline.updateStrategy(strategy.strategies.get('renderPipeline'));
    }
    
    // 更新设备适配规则
    if (strategy.strategies.has('adaptationRules')) {
      await this.deviceAdapter.updateRules(strategy.strategies.get('adaptationRules'));
    }
  }
  
  /**
   * 创建备份
   */
  protected async createBackup(): Promise<any> {
    return {
      config: { ...this.config },
      dataBuffer: new Map(this.dataBuffer),
      visualElements: new Map(this.visualElements)
    };
  }
  
  /**
   * 从备份恢复
   */
  protected async restoreFromBackup(backup: any): Promise<void> {
    this.config = backup.config;
    this.dataBuffer = backup.dataBuffer;
    this.visualElements = backup.visualElements;
  }
}

/**
 * 渲染管道类
 */
class RenderPipeline {
  private config: RenderingConfig;
  
  constructor(config: RenderingConfig) {
    this.config = config;
  }
  
  async processMetrics(metrics: DeviceMetrics, healthScore: HealthScore): Promise<VisualData[]> {
    const visualData: VisualData[] = [];
    
    // 生成CPU使用率卡片
    visualData.push({
      type: 'card',
      id: 'cpu-usage-card',
      title: 'CPU使用率',
      data: {
        value: metrics.cpu.usage,
        unit: '%',
        status: this.getStatusFromValue(metrics.cpu.usage, [70, 90]),
        trend: this.calculateTrend('cpu', metrics.cpu.usage)
      },
      config: {
        colors: ['#4CAF50', '#FF9800', '#F44336'],
        animation: { enabled: this.config.enableAnimations, duration: 300, easing: 'ease-in-out' }
      },
      timestamp: Date.now()
    });
    
    // 生成内存使用率卡片
    visualData.push({
      type: 'card',
      id: 'memory-usage-card',
      title: '内存使用率',
      data: {
        value: metrics.memory.usage,
        unit: '%',
        status: this.getStatusFromValue(metrics.memory.usage, [80, 95]),
        trend: this.calculateTrend('memory', metrics.memory.usage)
      },
      config: {
        colors: ['#4CAF50', '#FF9800', '#F44336'],
        animation: { enabled: this.config.enableAnimations, duration: 300, easing: 'ease-in-out' }
      },
      timestamp: Date.now()
    });
    
    // 生成电池电量卡片
    visualData.push({
      type: 'card',
      id: 'battery-level-card',
      title: '电池电量',
      data: {
        value: metrics.battery.level,
        unit: '%',
        status: this.getStatusFromValue(metrics.battery.level, [20, 10], true),
        trend: this.calculateTrend('battery', metrics.battery.level)
      },
      config: {
        colors: ['#4CAF50', '#FF9800', '#F44336'],
        animation: { enabled: this.config.enableAnimations, duration: 300, easing: 'ease-in-out' }
      },
      timestamp: Date.now()
    });
    
    // 生成健康度仪表盘
    visualData.push({
      type: 'gauge',
      id: 'health-score-gauge',
      title: '设备健康度',
      data: {
        value: healthScore.overall,
        min: 0,
        max: 100,
        unit: '分',
        components: healthScore.components
      },
      config: {
        colors: ['#F44336', '#FF9800', '#4CAF50'],
        animation: { enabled: this.config.enableAnimations, duration: 500, easing: 'ease-out' }
      },
      timestamp: Date.now()
    });
    
    return visualData;
  }
  
  private getStatusFromValue(value: number, thresholds: number[], reverse: boolean = false): string {
    if (reverse) {
      if (value <= thresholds[1]) return 'critical';
      if (value <= thresholds[0]) return 'warning';
      return 'normal';
    } else {
      if (value >= thresholds[1]) return 'critical';
      if (value >= thresholds[0]) return 'warning';
      return 'normal';
    }
  }
  
  private calculateTrend(metric: string, value: number): number {
    // 这里应该基于历史数据计算趋势
    // 临时实现返回随机趋势
    return (Math.random() - 0.5) * 10;
  }
  
  async updateStrategy(strategy: any): Promise<void> {
    // 更新渲染策略
    console.info('RenderPipeline: Strategy updated');
  }
}

/**
 * 设备适配管理器
 */
class DeviceAdapterManager {
  private currentDevice?: DeviceAdapter;
  private adaptationRules: AdaptationRule[] = [];
  
  async initialize(): Promise<void> {
    // 初始化设备适配器
    console.info('DeviceAdapterManager: Initialized');
  }
  
  async adaptVisualData(visualData: VisualData[]): Promise<VisualData[]> {
    // 应用设备适配规则
    return visualData.map(visual => ({
      ...visual,
      config: {
        ...visual.config,
        layout: this.adaptLayout(visual.config.layout)
      }
    }));
  }
  
  private adaptLayout(layout?: LayoutConfig): LayoutConfig {
    // 根据设备类型适配布局
    return layout || {};
  }
  
  async updateDevice(deviceInfo: any): Promise<void> {
    // 更新当前设备信息
    console.info('DeviceAdapterManager: Device updated');
  }
  
  async updateRules(rules: AdaptationRule[]): Promise<void> {
    this.adaptationRules = rules;
    console.info('DeviceAdapterManager: Adaptation rules updated');
  }
}

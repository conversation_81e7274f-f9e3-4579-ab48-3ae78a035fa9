/**
 * 主页面
 * 展示设备监控信息的主界面
 */

import { ResponsiveLayout, ScreenSize, Orientation, FoldState } from '../components/ResponsiveLayout';
import { MonitorCard, MonitorCardGrid, MonitorData, MonitorStatus, CardSize } from '../components/MonitorCard';
import { EventHub } from '../../../shared/communication/EventHub';
import { DeviceMetrics } from '../../../agents/monitoring/HardwareAbstractionLayer';
import { HealthScore } from '../../../agents/monitoring/MonitoringAgent';

@Entry
@Component
struct Index {
  @State monitorData: MonitorData[] = [];
  @State healthScore: number = 100;
  @State lastUpdateTime: string = '';
  @State isLoading: boolean = true;
  @State alertCount: number = 0;
  @State connectionStatus: string = '正常';
  
  private eventHub: EventHub = EventHub.getInstance();
  
  aboutToAppear() {
    this.initializeEventListeners();
    this.initializeMonitorData();
  }
  
  aboutToDisappear() {
    // 清理事件监听
  }
  
  /**
   * 初始化事件监听
   */
  private initializeEventListeners(): void {
    // 监听UI更新事件
    this.eventHub.subscribe('ui.update', (eventData) => {
      this.handleUIUpdate(eventData.data);
    });
    
    // 监听异常高亮事件
    this.eventHub.subscribe('ui.highlight', (eventData) => {
      this.handleHighlight(eventData.data);
    });
    
    // 监听监控数据事件
    this.eventHub.subscribe('monitoring.data', (eventData) => {
      this.handleMonitoringData(eventData.data);
    });
    
    // 监听异常事件
    this.eventHub.subscribe('monitoring.anomaly', (eventData) => {
      this.handleAnomalyData(eventData.data);
    });
  }
  
  /**
   * 初始化监控数据
   */
  private initializeMonitorData(): void {
    this.monitorData = [
      {
        title: 'CPU使用率',
        value: 0,
        unit: '%',
        status: MonitorStatus.UNKNOWN,
        lastUpdate: Date.now()
      },
      {
        title: '内存使用率',
        value: 0,
        unit: '%',
        status: MonitorStatus.UNKNOWN,
        lastUpdate: Date.now()
      },
      {
        title: '电池电量',
        value: 0,
        unit: '%',
        status: MonitorStatus.UNKNOWN,
        lastUpdate: Date.now()
      },
      {
        title: '存储使用率',
        value: 0,
        unit: '%',
        status: MonitorStatus.UNKNOWN,
        lastUpdate: Date.now()
      },
      {
        title: '网络状态',
        value: 0,
        unit: 'Mbps',
        status: MonitorStatus.UNKNOWN,
        lastUpdate: Date.now()
      },
      {
        title: '设备温度',
        value: 0,
        unit: '°C',
        status: MonitorStatus.UNKNOWN,
        lastUpdate: Date.now()
      }
    ];
    
    this.isLoading = false;
  }
  
  /**
   * 处理UI更新事件
   */
  private handleUIUpdate(data: any): void {
    if (data.visualData) {
      // 更新可视化数据
      this.updateVisualData(data.visualData);
    }
    
    this.lastUpdateTime = new Date().toLocaleTimeString();
  }
  
  /**
   * 处理高亮事件
   */
  private handleHighlight(data: any): void {
    // 实现高亮效果
    console.info('Index: Highlight effect applied', data);
  }
  
  /**
   * 处理监控数据事件
   */
  private handleMonitoringData(data: {
    metrics: DeviceMetrics;
    healthScore: HealthScore;
    anomalies: any[];
  }): void {
    this.updateMonitorDataFromMetrics(data.metrics);
    this.healthScore = data.healthScore.overall;
    this.lastUpdateTime = new Date().toLocaleTimeString();
    this.isLoading = false;
  }
  
  /**
   * 处理异常数据事件
   */
  private handleAnomalyData(data: any): void {
    this.alertCount = data.anomalies?.length || 0;
  }
  
  /**
   * 从指标数据更新监控数据
   */
  private updateMonitorDataFromMetrics(metrics: DeviceMetrics): void {
    // 更新CPU数据
    this.updateMonitorItem('CPU使用率', metrics.cpu.usage, '%', 
      this.getStatusFromValue(metrics.cpu.usage, 70, 90));
    
    // 更新内存数据
    this.updateMonitorItem('内存使用率', metrics.memory.usage, '%',
      this.getStatusFromValue(metrics.memory.usage, 80, 95));
    
    // 更新电池数据
    this.updateMonitorItem('电池电量', metrics.battery.level, '%',
      this.getStatusFromValue(metrics.battery.level, 20, 10, true));
    
    // 更新存储数据
    this.updateMonitorItem('存储使用率', metrics.storage.usage, '%',
      this.getStatusFromValue(metrics.storage.usage, 85, 95));
    
    // 更新网络数据
    this.updateMonitorItem('网络状态', metrics.network.signalStrength, '%',
      metrics.network.isConnected ? MonitorStatus.NORMAL : MonitorStatus.CRITICAL);
    
    // 更新温度数据
    const maxTemp = Math.max(metrics.temperature.cpu, metrics.temperature.battery);
    this.updateMonitorItem('设备温度', maxTemp, '°C',
      this.getStatusFromValue(maxTemp, 60, 80));
  }
  
  /**
   * 更新单个监控项
   */
  private updateMonitorItem(title: string, value: number, unit: string, status: MonitorStatus): void {
    const index = this.monitorData.findIndex(item => item.title === title);
    if (index !== -1) {
      this.monitorData[index] = {
        ...this.monitorData[index],
        value,
        unit,
        status,
        lastUpdate: Date.now()
      };
    }
  }
  
  /**
   * 根据数值获取状态
   */
  private getStatusFromValue(value: number, warning: number, critical: number, reverse: boolean = false): MonitorStatus {
    if (reverse) {
      if (value <= critical) return MonitorStatus.CRITICAL;
      if (value <= warning) return MonitorStatus.WARNING;
      return MonitorStatus.NORMAL;
    } else {
      if (value >= critical) return MonitorStatus.CRITICAL;
      if (value >= warning) return MonitorStatus.WARNING;
      return MonitorStatus.NORMAL;
    }
  }
  
  /**
   * 更新可视化数据
   */
  private updateVisualData(visualData: any[]): void {
    visualData.forEach(visual => {
      if (visual.type === 'card') {
        this.updateMonitorItem(
          visual.title,
          visual.data.value,
          visual.data.unit,
          this.mapStatusFromString(visual.data.status)
        );
      }
    });
  }
  
  /**
   * 映射状态字符串到枚举
   */
  private mapStatusFromString(status: string): MonitorStatus {
    switch (status) {
      case 'normal': return MonitorStatus.NORMAL;
      case 'warning': return MonitorStatus.WARNING;
      case 'critical': return MonitorStatus.CRITICAL;
      default: return MonitorStatus.UNKNOWN;
    }
  }
  
  /**
   * 处理卡片点击事件
   */
  private onCardClick = (data: MonitorData): void => {
    console.info('Index: Card clicked', data.title);
    // 这里可以导航到详细页面或显示更多信息
  }
  
  /**
   * 刷新数据
   */
  private refreshData(): void {
    this.isLoading = true;
    // 触发数据刷新
    this.eventHub.publish('ui.refresh', {
      timestamp: Date.now()
    });
  }
  
  build() {
    ResponsiveLayout({
      phoneLayout: () => {
        this.buildPhoneLayout();
      },
      tabletLayout: () => {
        this.buildTabletLayout();
      },
      watchLayout: () => {
        this.buildWatchLayout();
      },
      foldedLayout: () => {
        this.buildFoldedLayout();
      }
    })
  }
  
  @Builder buildPhoneLayout() {
    Column() {
      // 顶部状态栏
      this.buildStatusBar();
      
      // 主要内容区域
      Scroll() {
        Column() {
          // 健康度总览
          this.buildHealthOverview();
          
          // 监控卡片网格
          MonitorCardGrid({
            cards: this.monitorData,
            columns: 2,
            cardSize: CardSize.MEDIUM,
            spacing: 12,
            onCardClick: this.onCardClick
          })
          .margin({ top: 16 })
          
          // 底部信息
          this.buildBottomInfo();
        }
        .padding(16)
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
  
  @Builder buildTabletLayout() {
    Row() {
      // 左侧面板
      Column() {
        this.buildStatusBar();
        this.buildHealthOverview();
      }
      .width('30%')
      .height('100%')
      .padding(16)
      
      // 右侧主要内容
      Column() {
        MonitorCardGrid({
          cards: this.monitorData,
          columns: 3,
          cardSize: CardSize.LARGE,
          spacing: 16,
          onCardClick: this.onCardClick
        })
        
        this.buildBottomInfo();
      }
      .layoutWeight(1)
      .height('100%')
      .padding(16)
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
  
  @Builder buildWatchLayout() {
    Column() {
      // 简化的手表界面
      Text(`健康度: ${this.healthScore}%`)
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 8 })
      
      if (this.alertCount > 0) {
        Text(`${this.alertCount} 个告警`)
          .fontSize(14)
          .fontColor(Color.Red)
      } else {
        Text('系统正常')
          .fontSize(14)
          .fontColor(Color.Green)
      }
      
      Text(this.lastUpdateTime)
        .fontSize(12)
        .fontColor(Color.Gray)
        .margin({ top: 8 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .padding(8)
  }
  
  @Builder buildFoldedLayout() {
    // 折叠状态的紧凑布局
    Column() {
      Row() {
        Text(`健康度: ${this.healthScore}%`)
          .fontSize(14)
          .layoutWeight(1)
        
        if (this.alertCount > 0) {
          Text(`${this.alertCount}`)
            .fontSize(12)
            .fontColor(Color.White)
            .backgroundColor(Color.Red)
            .borderRadius(8)
            .padding({ left: 6, right: 6, top: 2, bottom: 2 })
        }
      }
      .width('100%')
      .padding(8)
      
      // 关键指标的紧凑显示
      Row() {
        ForEach(this.monitorData.slice(0, 3), (item: MonitorData) => {
          Column() {
            Text(`${item.value.toFixed(0)}${item.unit}`)
              .fontSize(12)
              .fontWeight(FontWeight.Bold)
            Text(item.title)
              .fontSize(10)
              .fontColor(Color.Gray)
          }
          .layoutWeight(1)
        })
      }
      .width('100%')
      .padding(8)
    }
    .width('100%')
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
  
  @Builder buildStatusBar() {
    Row() {
      Text('设备监控')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .layoutWeight(1)
      
      if (this.alertCount > 0) {
        Badge({
          count: this.alertCount,
          maxCount: 99,
          style: { color: Color.White, fontSize: 12, badgeColor: Color.Red }
        }) {
          Image($r('app.media.ic_alert'))
            .width(24)
            .height(24)
        }
        .margin({ right: 8 })
      }
      
      Button('刷新')
        .fontSize(14)
        .type(ButtonType.Normal)
        .borderRadius(16)
        .backgroundColor($r('sys.color.ohos_id_color_button_normal'))
        .onClick(() => {
          this.refreshData();
        })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 8, bottom: 8 })
    .backgroundColor($r('sys.color.ohos_id_color_background'))
  }
  
  @Builder buildHealthOverview() {
    Column() {
      Text('设备健康度')
        .fontSize(16)
        .fontWeight(FontWeight.Medium)
        .margin({ bottom: 8 })
      
      Progress({
        value: this.healthScore,
        total: 100,
        type: ProgressType.Ring
      })
      .width(120)
      .height(120)
      .color(this.getHealthColor())
      .style({
        strokeWidth: 8
      })
      
      Text(`${this.healthScore}%`)
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .fontColor(this.getHealthColor())
        .margin({ top: 8 })
      
      Text(this.getHealthDescription())
        .fontSize(14)
        .fontColor(Color.Gray)
        .margin({ top: 4 })
    }
    .width('100%')
    .alignItems(HorizontalAlign.Center)
    .padding(16)
    .backgroundColor($r('sys.color.ohos_id_color_background_transparent'))
    .borderRadius(12)
    .margin({ bottom: 16 })
  }
  
  @Builder buildBottomInfo() {
    Column() {
      Divider()
        .margin({ top: 16, bottom: 8 })
      
      Row() {
        Text(`最后更新: ${this.lastUpdateTime}`)
          .fontSize(12)
          .fontColor(Color.Gray)
          .layoutWeight(1)
        
        Text(`连接状态: ${this.connectionStatus}`)
          .fontSize(12)
          .fontColor(Color.Gray)
      }
      .width('100%')
    }
    .margin({ top: 16 })
  }
  
  /**
   * 获取健康度颜色
   */
  private getHealthColor(): Color | Resource {
    if (this.healthScore >= 80) return Color.Green;
    if (this.healthScore >= 60) return Color.Orange;
    return Color.Red;
  }
  
  /**
   * 获取健康度描述
   */
  private getHealthDescription(): string {
    if (this.healthScore >= 90) return '优秀';
    if (this.healthScore >= 80) return '良好';
    if (this.healthScore >= 60) return '一般';
    if (this.healthScore >= 40) return '较差';
    return '危险';
  }
}

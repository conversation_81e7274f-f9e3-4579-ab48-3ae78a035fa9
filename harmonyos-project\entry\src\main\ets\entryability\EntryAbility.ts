/**
 * 应用入口Ability
 * 负责应用的生命周期管理和Agent系统的初始化
 */

import UIAbility from '@ohos.app.ability.UIAbility';
import hilog from '@ohos.hilog';
import window from '@ohos.window';
import Want from '@ohos.app.ability.Want';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';

// 导入Agent系统
import { MonitoringAgent } from '../../../agents/monitoring/MonitoringAgent';
import { RenderingAgent } from '../../../agents/rendering/RenderingAgent';
import { EventHub } from '../../../shared/communication/EventHub';

export default class EntryAbility extends UIAbility {
  private monitoringAgent?: MonitoringAgent;
  private renderingAgent?: RenderingAgent;
  private eventHub: EventHub;
  
  constructor() {
    super();
    this.eventHub = EventHub.getInstance();
  }
  
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(0x0000, 'GoofishMonitor', '%{public}s', 'Ability onCreate');
    
    // 初始化Agent系统
    this.initializeAgents();
  }
  
  onDestroy(): void {
    hilog.info(0x0000, 'GoofishMonitor', '%{public}s', 'Ability onDestroy');
    
    // 清理Agent系统
    this.cleanupAgents();
  }
  
  onWindowStageCreate(windowStage: window.WindowStage): void {
    hilog.info(0x0000, 'GoofishMonitor', '%{public}s', 'Ability onWindowStageCreate');
    
    // 设置主窗口
    windowStage.loadContent('pages/Index', (err, data) => {
      if (err.code) {
        hilog.error(0x0000, 'GoofishMonitor', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'GoofishMonitor', 'Succeeded in loading the content. Data: %{public}s', JSON.stringify(data) ?? '');
    });
    
    // 启动Agent系统
    this.startAgents();
  }
  
  onWindowStageDestroy(): void {
    hilog.info(0x0000, 'GoofishMonitor', '%{public}s', 'Ability onWindowStageDestroy');
    
    // 停止Agent系统
    this.stopAgents();
  }
  
  onForeground(): void {
    hilog.info(0x0000, 'GoofishMonitor', '%{public}s', 'Ability onForeground');
    
    // 应用进入前台，恢复正常监控频率
    this.eventHub.publish('app.foreground', {
      timestamp: Date.now()
    });
  }
  
  onBackground(): void {
    hilog.info(0x0000, 'GoofishMonitor', '%{public}s', 'Ability onBackground');
    
    // 应用进入后台，降低监控频率以节省电量
    this.eventHub.publish('app.background', {
      timestamp: Date.now()
    });
  }
  
  /**
   * 初始化Agent系统
   */
  private async initializeAgents(): Promise<void> {
    try {
      hilog.info(0x0000, 'GoofishMonitor', 'Initializing Agent system...');
      
      // 创建监控Agent
      this.monitoringAgent = new MonitoringAgent({
        samplingRate: 10,
        enablePowerSaving: true,
        enableAnomalyDetection: true
      });
      
      // 创建渲染Agent
      this.renderingAgent = new RenderingAgent({
        updateInterval: 1000,
        enableAnimations: true,
        enableHighlighting: true,
        deviceAdaptation: true
      });
      
      hilog.info(0x0000, 'GoofishMonitor', 'Agent system initialized successfully');
    } catch (error) {
      hilog.error(0x0000, 'GoofishMonitor', 'Failed to initialize Agent system: %{public}s', JSON.stringify(error));
    }
  }
  
  /**
   * 启动Agent系统
   */
  private async startAgents(): Promise<void> {
    try {
      hilog.info(0x0000, 'GoofishMonitor', 'Starting Agent system...');
      
      // 启动监控Agent
      if (this.monitoringAgent) {
        await this.monitoringAgent.start();
        hilog.info(0x0000, 'GoofishMonitor', 'MonitoringAgent started');
      }
      
      // 启动渲染Agent
      if (this.renderingAgent) {
        await this.renderingAgent.start();
        hilog.info(0x0000, 'GoofishMonitor', 'RenderingAgent started');
      }
      
      // 设置应用生命周期监听
      this.setupLifecycleListeners();
      
      hilog.info(0x0000, 'GoofishMonitor', 'Agent system started successfully');
    } catch (error) {
      hilog.error(0x0000, 'GoofishMonitor', 'Failed to start Agent system: %{public}s', JSON.stringify(error));
    }
  }
  
  /**
   * 停止Agent系统
   */
  private async stopAgents(): Promise<void> {
    try {
      hilog.info(0x0000, 'GoofishMonitor', 'Stopping Agent system...');
      
      // 停止渲染Agent
      if (this.renderingAgent) {
        await this.renderingAgent.stop();
        hilog.info(0x0000, 'GoofishMonitor', 'RenderingAgent stopped');
      }
      
      // 停止监控Agent
      if (this.monitoringAgent) {
        await this.monitoringAgent.stop();
        hilog.info(0x0000, 'GoofishMonitor', 'MonitoringAgent stopped');
      }
      
      hilog.info(0x0000, 'GoofishMonitor', 'Agent system stopped successfully');
    } catch (error) {
      hilog.error(0x0000, 'GoofishMonitor', 'Failed to stop Agent system: %{public}s', JSON.stringify(error));
    }
  }
  
  /**
   * 清理Agent系统
   */
  private cleanupAgents(): void {
    this.monitoringAgent = undefined;
    this.renderingAgent = undefined;
    hilog.info(0x0000, 'GoofishMonitor', 'Agent system cleaned up');
  }
  
  /**
   * 设置应用生命周期监听
   */
  private setupLifecycleListeners(): void {
    // 监听应用前后台切换事件
    this.eventHub.subscribe('app.foreground', () => {
      // 恢复正常采样率
      if (this.monitoringAgent) {
        this.monitoringAgent.setSamplingRate(10);
      }
      
      // 恢复正常更新频率
      if (this.renderingAgent) {
        this.renderingAgent.setUpdateInterval(1000);
      }
    });
    
    this.eventHub.subscribe('app.background', () => {
      // 降低采样率以节省电量
      if (this.monitoringAgent) {
        this.monitoringAgent.setSamplingRate(2);
      }
      
      // 降低更新频率
      if (this.renderingAgent) {
        this.renderingAgent.setUpdateInterval(5000);
      }
    });
    
    // 监听功耗优化事件
    this.eventHub.subscribe('power.enterSavingMode', (eventData) => {
      hilog.info(0x0000, 'GoofishMonitor', 'Entering power saving mode: %{public}s', JSON.stringify(eventData.data));
      
      // 进一步降低采样率
      if (this.monitoringAgent) {
        this.monitoringAgent.setSamplingRate(1);
      }
      
      // 进一步降低更新频率
      if (this.renderingAgent) {
        this.renderingAgent.setUpdateInterval(10000);
      }
    });
  }
  
  /**
   * 获取Agent运行状态
   */
  getAgentStatus(): Record<string, any> {
    return {
      monitoring: this.monitoringAgent?.getRuntimeInfo(),
      rendering: this.renderingAgent?.getRuntimeInfo(),
      eventHub: this.eventHub.getStats()
    };
  }
}
